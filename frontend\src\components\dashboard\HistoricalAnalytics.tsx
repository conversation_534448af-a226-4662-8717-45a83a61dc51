import React, { useMemo, useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Area<PERSON>hart, ChartDataPoint, TimeSeriesDataPoint } from './Charts';
import { ChartContainer, SectionHeader, Card } from './DashboardComponents';
import { useBusiness } from '../../contexts/BusinessContext';
import { useSubscription } from '../../contexts/SubscriptionContext';
import { usePayment } from '../../contexts/PaymentContext';
import { useReimbursement } from '../../contexts/ReimbursementContext';

interface HistoricalPeriod {
  value: '3m' | '6m' | '12m' | '24m';
  label: string;
  months: number;
}

interface ComparisonMetrics {
  current: number;
  previous: number;
  change: number;
  changePercent: number;
  trend: 'up' | 'down' | 'stable';
}

interface HistoricalAnalyticsProps {
  selectedPeriod?: '3m' | '6m' | '12m' | '24m';
  onPeriodChange?: (period: '3m' | '6m' | '12m' | '24m') => void;
}

export const HistoricalAnalytics: React.FC<HistoricalAnalyticsProps> = ({
  selectedPeriod = '12m',
  onPeriodChange
}) => {
  const { currentBusiness } = useBusiness();
  const { subscriptions, loading: subscriptionsLoading } = useSubscription();
  const { payments, loading: paymentsLoading } = usePayment();
  const { reimbursements, loading: reimbursementsLoading } = useReimbursement();

  const [analysisType, setAnalysisType] = useState<'overview' | 'spending' | 'subscriptions' | 'reimbursements'>('overview');

  const isLoading = subscriptionsLoading || paymentsLoading || reimbursementsLoading;

  const periods: HistoricalPeriod[] = [
    { value: '3m', label: '3 Months', months: 3 },
    { value: '6m', label: '6 Months', months: 6 },
    { value: '12m', label: '12 Months', months: 12 },
    { value: '24m', label: '24 Months', months: 24 }
  ];

  const selectedPeriodConfig = periods.find(p => p.value === selectedPeriod) || periods[2];

  // Generate historical data for the selected period
  const historicalData = useMemo(() => {
    const monthsToAnalyze = selectedPeriodConfig.months;
    const months = Array.from({ length: monthsToAnalyze }, (_, i) => {
      const date = new Date();
      date.setMonth(date.getMonth() - (monthsToAnalyze - 1 - i));
      return {
        date: date.toISOString(),
        month: date.getMonth(),
        year: date.getFullYear(),
        monthName: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
      };
    });

    return months.map(monthInfo => {
      // Calculate spending for this month
      const monthlySpending = payments
        .filter(p => {
          const paymentDate = new Date(p.payment_date);
          return p.status === 'completed' &&
                 paymentDate.getMonth() === monthInfo.month &&
                 paymentDate.getFullYear() === monthInfo.year;
        })
        .reduce((sum, p) => sum + p.amount, 0);

      // Calculate active subscriptions at end of month
      const endOfMonth = new Date(monthInfo.year, monthInfo.month + 1, 0);
      const activeSubscriptions = subscriptions.filter(s => {
        const createdDate = new Date(s.created_at);
        const cancelledDate = s.cancelled_at ? new Date(s.cancelled_at) : null;
        return createdDate <= endOfMonth && (!cancelledDate || cancelledDate > endOfMonth);
      }).length;

      // Calculate reimbursements for this month
      const monthlyReimbursements = reimbursements
        .filter(r => {
          const requestDate = new Date(r.request_date);
          return requestDate.getMonth() === monthInfo.month &&
                 requestDate.getFullYear() === monthInfo.year;
        })
        .reduce((sum, r) => sum + r.amount, 0);

      const reimbursementCount = reimbursements.filter(r => {
        const requestDate = new Date(r.request_date);
        return requestDate.getMonth() === monthInfo.month &&
               requestDate.getFullYear() === monthInfo.year;
      }).length;

      // Calculate payment count
      const paymentCount = payments.filter(p => {
        const paymentDate = new Date(p.payment_date);
        return p.status === 'completed' &&
               paymentDate.getMonth() === monthInfo.month &&
               paymentDate.getFullYear() === monthInfo.year;
      }).length;

      return {
        ...monthInfo,
        spending: monthlySpending,
        activeSubscriptions,
        reimbursements: monthlyReimbursements,
        reimbursementCount,
        paymentCount
      };
    });
  }, [payments, subscriptions, reimbursements, selectedPeriodConfig.months]);

  // Calculate comparison metrics (current vs previous period)
  const comparisonMetrics = useMemo((): {
    spending: ComparisonMetrics;
    subscriptions: ComparisonMetrics;
    reimbursements: ComparisonMetrics;
  } => {
    const halfPoint = Math.floor(historicalData.length / 2);
    const firstHalf = historicalData.slice(0, halfPoint);
    const secondHalf = historicalData.slice(halfPoint);

    const calculateMetrics = (current: number, previous: number): ComparisonMetrics => {
      const change = current - previous;
      const changePercent = previous > 0 ? (change / previous) * 100 : 0;
      const trend = Math.abs(changePercent) < 5 ? 'stable' : changePercent > 0 ? 'up' : 'down';
      
      return { current, previous, change, changePercent, trend };
    };

    const firstHalfSpending = firstHalf.reduce((sum, d) => sum + d.spending, 0);
    const secondHalfSpending = secondHalf.reduce((sum, d) => sum + d.spending, 0);

    const firstHalfAvgSubs = firstHalf.length > 0 ? 
      firstHalf.reduce((sum, d) => sum + d.activeSubscriptions, 0) / firstHalf.length : 0;
    const secondHalfAvgSubs = secondHalf.length > 0 ?
      secondHalf.reduce((sum, d) => sum + d.activeSubscriptions, 0) / secondHalf.length : 0;

    const firstHalfReimbursements = firstHalf.reduce((sum, d) => sum + d.reimbursements, 0);
    const secondHalfReimbursements = secondHalf.reduce((sum, d) => sum + d.reimbursements, 0);

    return {
      spending: calculateMetrics(secondHalfSpending, firstHalfSpending),
      subscriptions: calculateMetrics(secondHalfAvgSubs, firstHalfAvgSubs),
      reimbursements: calculateMetrics(secondHalfReimbursements, firstHalfReimbursements)
    };
  }, [historicalData]);

  // Generate forecasting data based on historical trends
  const forecastData = useMemo((): TimeSeriesDataPoint[] => {
    if (historicalData.length < 3) return [];

    const recentTrend = historicalData.slice(-3);
    const avgGrowth = recentTrend.reduce((sum, data, index) => {
      if (index === 0) return 0;
      const growth = (data.spending - recentTrend[index - 1].spending) / (recentTrend[index - 1].spending || 1);
      return sum + growth;
    }, 0) / (recentTrend.length - 1);

    const lastMonth = historicalData[historicalData.length - 1];
    
    return Array.from({ length: 3 }, (_, i) => {
      const futureDate = new Date();
      futureDate.setMonth(futureDate.getMonth() + i + 1);
      
      const forecastValue = lastMonth.spending * Math.pow(1 + avgGrowth, i + 1);
      
      return {
        date: futureDate.toISOString(),
        value: Math.max(0, forecastValue)
      };
    });
  }, [historicalData]);

  // Seasonal analysis
  const seasonalAnalysis = useMemo(() => {
    const quarterlyData = [
      { quarter: 'Q1', months: [0, 1, 2], spending: 0, count: 0 },
      { quarter: 'Q2', months: [3, 4, 5], spending: 0, count: 0 },
      { quarter: 'Q3', months: [6, 7, 8], spending: 0, count: 0 },
      { quarter: 'Q4', months: [9, 10, 11], spending: 0, count: 0 }
    ];

    historicalData.forEach(data => {
      const monthIndex = new Date(data.date).getMonth();
      const quarter = quarterlyData.find(q => q.months.includes(monthIndex));
      if (quarter) {
        quarter.spending += data.spending;
        quarter.count += 1;
      }
    });

    return quarterlyData.map(q => ({
      ...q,
      avgSpending: q.count > 0 ? q.spending / q.count : 0
    }));
  }, [historicalData]);

  const renderComparisonCard = (title: string, metrics: ComparisonMetrics, format: 'currency' | 'number' = 'currency') => (
    <Card className="p-4">
      <h4 className="text-sm font-medium text-gray-700 mb-2">{title}</h4>
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-500">Current Period</span>
          <span className="text-sm font-medium">
            {format === 'currency' ? `KWD ${metrics.current.toFixed(2)}` : Math.round(metrics.current)}
          </span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-500">Previous Period</span>
          <span className="text-sm font-medium">
            {format === 'currency' ? `KWD ${metrics.previous.toFixed(2)}` : Math.round(metrics.previous)}
          </span>
        </div>
        <div className="flex items-center justify-between pt-2 border-t">
          <span className="text-xs font-medium">Change</span>
          <div className="flex items-center space-x-1">
            <span className={`text-sm font-medium ${
              metrics.trend === 'up' ? 'text-green-600' : 
              metrics.trend === 'down' ? 'text-red-600' : 'text-gray-600'
            }`}>
              {metrics.changePercent > 0 ? '+' : ''}{metrics.changePercent.toFixed(1)}%
            </span>
            {metrics.trend === 'up' && (
              <svg className="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
              </svg>
            )}
            {metrics.trend === 'down' && (
              <svg className="h-4 w-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 7l-9.2 9.2M7 7v10h10" />
              </svg>
            )}
          </div>
        </div>
      </div>
    </Card>
  );

  if (!currentBusiness) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500">Please select a business to view historical analytics</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex flex-wrap gap-4 items-center justify-between">
        <div className="flex items-center space-x-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Time Period</label>
            <select
              value={selectedPeriod}
              onChange={(e) => onPeriodChange?.(e.target.value as any)}
              className="block w-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              {periods.map(period => (
                <option key={period.value} value={period.value}>
                  {period.label}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Analysis Type</label>
            <select
              value={analysisType}
              onChange={(e) => setAnalysisType(e.target.value as any)}
              className="block w-40 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              <option value="overview">Overview</option>
              <option value="spending">Spending Analysis</option>
              <option value="subscriptions">Subscriptions</option>
              <option value="reimbursements">Reimbursements</option>
            </select>
          </div>
        </div>
      </div>

      {/* Period Comparison */}
      <div>
        <SectionHeader
          title="Period Comparison"
          subtitle={`Comparing first half vs second half of ${selectedPeriodConfig.label.toLowerCase()}`}
          className="mb-4"
        />
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {renderComparisonCard('Total Spending', comparisonMetrics.spending)}
          {renderComparisonCard('Avg Subscriptions', comparisonMetrics.subscriptions, 'number')}
          {renderComparisonCard('Reimbursements', comparisonMetrics.reimbursements)}
        </div>
      </div>

      {/* Historical Trends */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartContainer
          title="Spending Trend with Forecast"
          subtitle="Historical spending with 3-month forecast"
          loading={isLoading}
        >
          <div className="space-y-4">
            <AreaChart
              data={historicalData.map(d => ({
                date: d.date,
                value: d.spending
              }))}
              width={400}
              height={200}
              color="#10B981"
              className="w-full"
            />
            
            {forecastData.length > 0 && (
              <div className="border-t pt-4">
                <h5 className="text-sm font-medium text-gray-700 mb-2">3-Month Forecast</h5>
                <LineChart
                  data={forecastData}
                  width={400}
                  height={120}
                  color="#6B7280"
                  className="w-full opacity-70"
                />
              </div>
            )}
          </div>
        </ChartContainer>

        <ChartContainer
          title="Subscription Growth Trend"
          subtitle="Active subscriptions over time"
          loading={isLoading}
        >
          <LineChart
            data={historicalData.map(d => ({
              date: d.date,
              value: d.activeSubscriptions
            }))}
            width={400}
            height={200}
            color="#3B82F6"
            className="w-full"
          />
        </ChartContainer>
      </div>

      {/* Seasonal Analysis */}
      <ChartContainer
        title="Seasonal Analysis"
        subtitle="Quarterly spending patterns"
        loading={isLoading}
      >
        <BarChart
          data={seasonalAnalysis.map(q => ({
            label: q.quarter,
            value: q.avgSpending
          }))}
          width={600}
          height={200}
          color="#8B5CF6"
          className="w-full"
        />
      </ChartContainer>

      {/* Detailed Analysis by Type */}
      {analysisType === 'spending' && (
        <ChartContainer
          title="Detailed Spending Analysis"
          subtitle="Monthly breakdown with payment frequency"
          loading={isLoading}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h5 className="text-sm font-medium text-gray-700 mb-2">Monthly Spending</h5>
              <BarChart
                data={historicalData.slice(-6).map(d => ({
                  label: d.monthName.split(' ')[0],
                  value: d.spending
                }))}
                width={300}
                height={150}
                color="#10B981"
              />
            </div>
            <div>
              <h5 className="text-sm font-medium text-gray-700 mb-2">Payment Frequency</h5>
              <BarChart
                data={historicalData.slice(-6).map(d => ({
                  label: d.monthName.split(' ')[0],
                  value: d.paymentCount
                }))}
                width={300}
                height={150}
                color="#3B82F6"
              />
            </div>
          </div>
        </ChartContainer>
      )}

      {/* Statistical Summary */}
      <Card className="p-6">
        <SectionHeader
          title="Statistical Summary"
          subtitle={`Analysis for ${selectedPeriodConfig.label.toLowerCase()}`}
          className="mb-4"
        />
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-blue-600">
              KWD {(historicalData.reduce((sum, d) => sum + d.spending, 0) / historicalData.length).toFixed(2)}
            </div>
            <div className="text-sm text-gray-500">Avg Monthly Spending</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">
              {Math.max(...historicalData.map(d => d.activeSubscriptions))}
            </div>
            <div className="text-sm text-gray-500">Peak Subscriptions</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-600">
              KWD {Math.max(...historicalData.map(d => d.spending)).toFixed(2)}
            </div>
            <div className="text-sm text-gray-500">Highest Monthly Spend</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-yellow-600">
              {historicalData.reduce((sum, d) => sum + d.reimbursementCount, 0)}
            </div>
            <div className="text-sm text-gray-500">Total Reimbursements</div>
          </div>
        </div>
      </Card>
    </div>
  );
};