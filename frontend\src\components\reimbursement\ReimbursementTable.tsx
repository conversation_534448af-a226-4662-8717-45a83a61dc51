import React, { useMemo, useState } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  ColumnDef,
  flexRender,
  SortingState,
  ColumnFiltersState,
} from '@tanstack/react-table';
import { Reimbursement } from '../../contexts/ReimbursementContext';

interface ReimbursementTableProps {
  reimbursements: Reimbursement[];
  onEdit?: (reimbursement: Reimbursement) => void;
  onDelete?: (reimbursement: Reimbursement) => void;
  onView?: (reimbursement: Reimbursement) => void;
  onStatusChange?: (reimbursement: Reimbursement, newStatus: 'pending' | 'approved' | 'received') => void;
  loading?: boolean;
}

export const ReimbursementTable: React.FC<ReimbursementTableProps> = ({
  reimbursements,
  onEdit,
  onDelete,
  onView,
  onStatusChange,
  loading = false
}) => {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState('');

  const columns = useMemo<ColumnDef<Reimbursement>[]>(
    () => [
      {
        accessorKey: 'reference_number',
        header: 'Reference',
        cell: ({ row }) => (
          <div className="font-medium text-gray-900">
            {row.original.reference_number}
          </div>
        ),
      },
      {
        accessorKey: 'business.name',
        header: 'Business',
        cell: ({ row }) => (
          <div>
            <div className="font-medium text-gray-900">
              {row.original.business.name}
            </div>
            <div className="text-sm text-gray-500">
              Timeline: {row.original.business.reimbursement_timeline} days
            </div>
          </div>
        ),
      },
      {
        accessorKey: 'amount',
        header: 'Amount',
        cell: ({ row }) => (
          <div className="text-right">
            <span className="font-medium text-gray-900">
              KWD {row.original.amount.toFixed(3)}
            </span>
          </div>
        ),
      },
      {
        accessorKey: 'request_date',
        header: 'Request Date',
        cell: ({ row }) => {
          const date = new Date(row.original.request_date);
          return (
            <div>
              <div className="font-medium text-gray-900">
                {date.toLocaleDateString()}
              </div>
              <div className="text-xs text-gray-500">
                {date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: 'expected_date',
        header: 'Expected Date',
        cell: ({ row }) => {
          const expectedDate = new Date(row.original.expected_date);
          const now = new Date();
          const isOverdue = row.original.status !== 'received' && expectedDate < now;
          
          return (
            <div>
              <div className={`font-medium ${isOverdue ? 'text-red-600' : 'text-gray-900'}`}>
                {expectedDate.toLocaleDateString()}
              </div>
              {isOverdue && (
                <div className="text-xs text-red-500 font-medium">
                  Overdue
                </div>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: 'actual_date',
        header: 'Received Date',
        cell: ({ row }) => {
          const actualDate = row.original.actual_date;
          return (
            <div className="text-sm text-gray-900">
              {actualDate ? new Date(actualDate).toLocaleDateString() : '-'}
            </div>
          );
        },
      },
      {
        accessorKey: 'status',
        header: 'Status',
        cell: ({ row }) => {
          const status = row.original.status;
          const statusColors = {
            pending: 'bg-yellow-100 text-yellow-800',
            approved: 'bg-blue-100 text-blue-800',
            received: 'bg-green-100 text-green-800'
          };
          
          return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[status]}`}>
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </span>
          );
        },
      },
      {
        accessorKey: 'payments',
        header: 'Payments',
        cell: ({ row }) => (
          <div className="text-sm text-gray-500">
            {row.original.payments.length} payment(s)
          </div>
        ),
      },
      {
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => (
          <div className="flex space-x-2">
            {onView && (
              <button
                onClick={() => onView(row.original)}
                className="text-blue-600 hover:text-blue-900 text-sm font-medium"
                title="View details"
              >
                View
              </button>
            )}
            {onEdit && row.original.status === 'pending' && (
              <button
                onClick={() => onEdit(row.original)}
                className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                title="Edit reimbursement"
              >
                Edit
              </button>
            )}
            {onStatusChange && (
              <div className="relative">
                <select
                  value={row.original.status}
                  onChange={(e) => onStatusChange(row.original, e.target.value as any)}
                  className="text-sm border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                  title="Change status"
                >
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="received">Received</option>
                </select>
              </div>
            )}
            {onDelete && row.original.status === 'pending' && (
              <button
                onClick={() => onDelete(row.original)}
                className="text-red-600 hover:text-red-900 text-sm font-medium"
                title="Delete reimbursement"
              >
                Delete
              </button>
            )}
          </div>
        ),
      },
    ],
    [onEdit, onDelete, onView, onStatusChange]
  );

  const table = useReactTable({
    data: reimbursements,
    columns,
    state: {
      sorting,
      columnFilters,
      globalFilter,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Global Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="flex-1">
          <input
            value={globalFilter ?? ''}
            onChange={(e) => setGlobalFilter(e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search reimbursements..."
          />
        </div>
        <div className="flex space-x-2">
          {/* Status Filter */}
          <select
            value={(table.getColumn('status')?.getFilterValue() as string) ?? ''}
            onChange={(e) =>
              table.getColumn('status')?.setFilterValue(e.target.value || undefined)
            }
            className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Status</option>
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="received">Received</option>
          </select>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
        <table className="min-w-full divide-y divide-gray-300">
          <thead className="bg-gray-50">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer select-none"
                    onClick={header.column.getToggleSortingHandler()}
                  >
                    <div className="flex items-center space-x-1">
                      <span>
                        {header.isPlaceholder
                          ? null
                          : flexRender(header.column.columnDef.header, header.getContext())}
                      </span>
                      <span>
                        {{
                          asc: '↑',
                          desc: '↓',
                        }[header.column.getIsSorted() as string] ?? null}
                      </span>
                    </div>
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {table.getRowModel().rows.map((row) => (
              <tr key={row.id} className="hover:bg-gray-50">
                {row.getVisibleCells().map((cell) => (
                  <td key={cell.id} className="px-6 py-4 whitespace-nowrap">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Empty State */}
      {reimbursements.length === 0 && (
        <div className="text-center py-12">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No reimbursements</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by creating a new reimbursement request.</p>
        </div>
      )}

      {/* Pagination */}
      {reimbursements.length > 0 && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700">
              Showing {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} to{' '}
              {Math.min(
                (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
                table.getFilteredRowModel().rows.length
              )}{' '}
              of {table.getFilteredRowModel().rows.length} results
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <span className="text-sm text-gray-700">
              Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
            </span>
            <button
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
};