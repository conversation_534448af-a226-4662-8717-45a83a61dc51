import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Payment } from '../../contexts/PaymentContext';
import { useSubscription } from '../../contexts/SubscriptionContext';
import { useBusiness } from '../../contexts/BusinessContext';

const paymentSchema = z.object({
  subscription_id: z.string().min(1, 'Subscription is required'),
  amount: z.number().positive('Amount must be positive').max(999999.99, 'Amount too large'),
  currency: z.enum(['USD', 'GBP', 'KWD']),
  payment_date: z.string().min(1, 'Payment date is required'),
  payment_method: z.string().min(1, 'Payment method is required').max(100, 'Payment method too long'),
  reference_number: z.string().max(100, 'Reference number too long').optional(),
  notes: z.string().max(500, 'Notes too long').optional(),
  status: z.enum(['pending', 'completed', 'failed', 'refunded'])
});

type PaymentFormData = z.infer<typeof paymentSchema>;

interface PaymentFormProps {
  payment?: Payment;
  onSubmit: (data: PaymentFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  paymentMethods?: string[];
}

export const PaymentForm: React.FC<PaymentFormProps> = ({
  payment,
  onSubmit,
  onCancel,
  isLoading = false,
  paymentMethods = []
}) => {
  const { subscriptions, fetchSubscriptions } = useSubscription();
  const { currentBusiness } = useBusiness();
  const [error, setError] = useState<string | null>(null);
  const [customPaymentMethod, setCustomPaymentMethod] = useState('');
  const [showCustomMethod, setShowCustomMethod] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      subscription_id: payment?.subscription_id || '',
      amount: payment?.amount || 0,
      currency: payment?.currency || 'USD',
      payment_date: payment?.payment_date 
        ? new Date(payment.payment_date).toISOString().split('T')[0]
        : new Date().toISOString().split('T')[0],
      payment_method: payment?.payment_method || '',
      reference_number: payment?.reference_number || '',
      notes: payment?.notes || '',
      status: payment?.status || 'completed'
    }
  });

  const selectedPaymentMethod = watch('payment_method');

  useEffect(() => {
    if (currentBusiness) {
      fetchSubscriptions({ business_id: currentBusiness.id, status: 'active' });
    }
  }, [currentBusiness, fetchSubscriptions]);

  useEffect(() => {
    if (selectedPaymentMethod === 'custom') {
      setShowCustomMethod(true);
    } else {
      setShowCustomMethod(false);
      setCustomPaymentMethod('');
    }
  }, [selectedPaymentMethod]);

  const handleFormSubmit = async (data: PaymentFormData) => {
    setError(null);
    try {
      // If custom payment method is selected, use the custom value
      if (data.payment_method === 'custom' && customPaymentMethod.trim()) {
        data.payment_method = customPaymentMethod.trim();
      } else if (data.payment_method === 'custom') {
        data.payment_method = '';
      }

      // Convert date to ISO string
      const paymentDate = new Date(data.payment_date);
      paymentDate.setHours(12, 0, 0, 0); // Set to noon to avoid timezone issues
      
      await onSubmit({
        ...data,
        payment_date: paymentDate.toISOString()
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const predefinedMethods = [
    'Credit Card',
    'Debit Card',
    'Bank Transfer',
    'PayPal',
    'Apple Pay',
    'Google Pay',
    'Cash',
    'Check',
    'Wire Transfer',
    'Other'
  ];

  const allMethods = [...new Set([...predefinedMethods, ...paymentMethods])];

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="subscription_id" className="block text-sm font-medium text-gray-700">
            Subscription *
          </label>
          <select
            {...register('subscription_id')}
            id="subscription_id"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select a subscription</option>
            {subscriptions.map((subscription) => (
              <option key={subscription.id} value={subscription.id}>
                {subscription.service_name} - {subscription.provider}
              </option>
            ))}
          </select>
          {errors.subscription_id && (
            <p className="mt-1 text-sm text-red-600">{errors.subscription_id.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="status" className="block text-sm font-medium text-gray-700">
            Status *
          </label>
          <select
            {...register('status')}
            id="status"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="completed">Completed</option>
            <option value="pending">Pending</option>
            <option value="failed">Failed</option>
            <option value="refunded">Refunded</option>
          </select>
          {errors.status && (
            <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
            Amount *
          </label>
          <input
            {...register('amount', { valueAsNumber: true })}
            type="number"
            step="0.01"
            min="0"
            id="amount"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="0.00"
          />
          {errors.amount && (
            <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="currency" className="block text-sm font-medium text-gray-700">
            Currency *
          </label>
          <select
            {...register('currency')}
            id="currency"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="USD">USD</option>
            <option value="GBP">GBP</option>
            <option value="KWD">KWD</option>
          </select>
          {errors.currency && (
            <p className="mt-1 text-sm text-red-600">{errors.currency.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="payment_date" className="block text-sm font-medium text-gray-700">
            Payment Date *
          </label>
          <input
            {...register('payment_date')}
            type="date"
            id="payment_date"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
          {errors.payment_date && (
            <p className="mt-1 text-sm text-red-600">{errors.payment_date.message}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="payment_method" className="block text-sm font-medium text-gray-700">
            Payment Method *
          </label>
          <select
            {...register('payment_method')}
            id="payment_method"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select payment method</option>
            {allMethods.map((method) => (
              <option key={method} value={method}>
                {method}
              </option>
            ))}
            <option value="custom">Custom Method...</option>
          </select>
          {errors.payment_method && (
            <p className="mt-1 text-sm text-red-600">{errors.payment_method.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="reference_number" className="block text-sm font-medium text-gray-700">
            Reference Number
          </label>
          <input
            {...register('reference_number')}
            type="text"
            id="reference_number"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Transaction ID, check number, etc."
          />
          {errors.reference_number && (
            <p className="mt-1 text-sm text-red-600">{errors.reference_number.message}</p>
          )}
        </div>
      </div>

      {showCustomMethod && (
        <div>
          <label htmlFor="custom_payment_method" className="block text-sm font-medium text-gray-700">
            Custom Payment Method
          </label>
          <input
            type="text"
            id="custom_payment_method"
            value={customPaymentMethod}
            onChange={(e) => setCustomPaymentMethod(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter custom payment method"
          />
        </div>
      )}

      <div>
        <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
          Notes
        </label>
        <textarea
          {...register('notes')}
          id="notes"
          rows={3}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="Additional notes about this payment..."
        />
        {errors.notes && (
          <p className="mt-1 text-sm text-red-600">{errors.notes.message}</p>
        )}
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isLoading}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Saving...' : payment ? 'Update Payment' : 'Create Payment'}
        </button>
      </div>
    </form>
  );
};