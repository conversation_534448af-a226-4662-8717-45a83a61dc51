import { Router } from 'express';
import { z } from 'zod';
import { asyncHandler } from '../middleware/errorHandler';
import { authenticateToken, requireAdmin, AuthenticatedRequest } from '../middleware/auth';
import { ExchangeRateService } from '../services/exchangeRateService';
import { ExchangeRateJob } from '../jobs/exchangeRateJob';

const router = Router();

// Validation schemas
const convertCurrencySchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  fromCurrency: z.string().length(3, 'Currency code must be 3 characters'),
  toCurrency: z.string().length(3, 'Currency code must be 3 characters'),
  date: z.string().optional()
});

const manualRateSchema = z.object({
  fromCurrency: z.string().length(3, 'Currency code must be 3 characters'),
  toCurrency: z.string().length(3, 'Currency code must be 3 characters'),
  rate: z.number().positive('Rate must be positive'),
  date: z.string().optional()
});

const historicalRatesSchema = z.object({
  fromCurrency: z.string().length(3, 'Currency code must be 3 characters'),
  toCurrency: z.string().length(3, 'Currency code must be 3 characters'),
  startDate: z.string(),
  endDate: z.string()
});

// GET /api/exchange-rates/supported
router.get('/supported', asyncHandler(async (req, res) => {
  const currencies = ExchangeRateService.getSupportedCurrencies();
  
  res.json({
    success: true,
    message: 'Supported currencies retrieved successfully',
    data: currencies
  });
}));

// POST /api/exchange-rates/convert
router.post('/convert', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const conversionData = convertCurrencySchema.parse(req.body);
  
  const result = await ExchangeRateService.convertCurrency(
    conversionData.amount,
    conversionData.fromCurrency.toUpperCase(),
    conversionData.toCurrency.toUpperCase(),
    conversionData.date
  );
  
  res.json({
    success: true,
    message: 'Currency conversion completed successfully',
    data: result
  });
}));

// GET /api/exchange-rates/current/:fromCurrency/:toCurrency
router.get('/current/:fromCurrency/:toCurrency', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { fromCurrency, toCurrency } = req.params;
  const { date } = req.query;
  
  const rate = await ExchangeRateService.getRate(
    fromCurrency.toUpperCase(),
    toCurrency.toUpperCase(),
    date as string
  );
  
  res.json({
    success: true,
    message: 'Exchange rate retrieved successfully',
    data: {
      fromCurrency: fromCurrency.toUpperCase(),
      toCurrency: toCurrency.toUpperCase(),
      rate,
      date: date || new Date().toISOString().split('T')[0]
    }
  });
}));

// GET /api/exchange-rates/historical
router.get('/historical', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const queryData = historicalRatesSchema.parse(req.query);
  
  const rates = await ExchangeRateService.getHistoricalRates(
    queryData.fromCurrency.toUpperCase(),
    queryData.toCurrency.toUpperCase(),
    queryData.startDate,
    queryData.endDate
  );
  
  res.json({
    success: true,
    message: 'Historical exchange rates retrieved successfully',
    data: rates
  });
}));

// POST /api/exchange-rates/manual-update (Admin only)
router.post('/manual-update', authenticateToken, requireAdmin, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const rateData = manualRateSchema.parse(req.body);
  
  const updatedRate = await ExchangeRateService.updateRateManually(
    rateData.fromCurrency.toUpperCase(),
    rateData.toCurrency.toUpperCase(),
    rateData.rate,
    rateData.date
  );
  
  res.json({
    success: true,
    message: 'Exchange rate updated manually',
    data: updatedRate
  });
}));

// POST /api/exchange-rates/fetch-current (Admin only)
router.post('/fetch-current', authenticateToken, requireAdmin, asyncHandler(async (req: AuthenticatedRequest, res) => {
  await ExchangeRateJob.runManually();
  
  res.json({
    success: true,
    message: 'Current exchange rates fetched and stored successfully'
  });
}));

// GET /api/exchange-rates/job-status (Admin only)
router.get('/job-status', authenticateToken, requireAdmin, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const status = ExchangeRateJob.getStatus();
  
  res.json({
    success: true,
    message: 'Exchange rate job status retrieved successfully',
    data: status
  });
}));

export { router as exchangeRateRoutes };