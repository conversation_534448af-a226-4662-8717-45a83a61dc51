import React from 'react';
import { Reimbursement } from '../../contexts/ReimbursementContext';

interface TimelineStep {
  id: string;
  title: string;
  description: string;
  date: string | null;
  status: 'completed' | 'current' | 'upcoming';
  icon: React.ReactNode;
}

interface ReimbursementTimelineProps {
  reimbursement: Reimbursement;
  className?: string;
}

export const ReimbursementTimeline: React.FC<ReimbursementTimelineProps> = ({
  reimbursement,
  className = ''
}) => {
  const getTimelineSteps = (reimbursement: Reimbursement): TimelineStep[] => {
    const steps: TimelineStep[] = [
      {
        id: 'request',
        title: 'Request Submitted',
        description: 'Reimbursement request was created',
        date: reimbursement.request_date,
        status: 'completed',
        icon: (
          <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        )
      },
      {
        id: 'approved',
        title: 'Approved',
        description: 'Request has been approved for payment',
        date: null, // Would need status change tracking
        status: reimbursement.status === 'pending' ? 'upcoming' : reimbursement.status === 'approved' || reimbursement.status === 'received' ? 'completed' : 'upcoming',
        icon: (
          <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
      },
      {
        id: 'received',
        title: 'Payment Received',
        description: 'Reimbursement payment has been received',
        date: reimbursement.actual_date,
        status: reimbursement.status === 'received' ? 'completed' : reimbursement.status === 'approved' ? 'current' : 'upcoming',
        icon: (
          <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
          </svg>
        )
      }
    ];

    return steps;
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'current':
        return 'text-blue-600 bg-blue-100';
      case 'upcoming':
        return 'text-gray-400 bg-gray-100';
      default:
        return 'text-gray-400 bg-gray-100';
    }
  };

  const getConnectorColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'border-green-300';
      case 'current':
        return 'border-blue-300';
      case 'upcoming':
        return 'border-gray-300';
      default:
        return 'border-gray-300';
    }
  };

  const steps = getTimelineSteps(reimbursement);
  const isOverdue = reimbursement.status !== 'received' && new Date(reimbursement.expected_date) < new Date();

  return (
    <div className={`bg-white ${className}`}>
      <div className="px-4 py-5 sm:px-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Timeline Progress
          </h3>
          {isOverdue && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
              Overdue
            </span>
          )}
        </div>
        <p className="mt-1 max-w-2xl text-sm text-gray-500">
          Track the progress of your reimbursement request
        </p>
      </div>

      <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
        {/* Expected Date Info */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-900">Expected Completion Date</p>
              <p className={`text-sm ${isOverdue ? 'text-red-600' : 'text-gray-600'}`}>
                {formatDate(reimbursement.expected_date)?.date}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-500">Timeline</p>
              <p className="text-sm font-medium text-gray-900">
                {reimbursement.business.reimbursement_timeline} days
              </p>
            </div>
          </div>
        </div>

        {/* Timeline Steps */}
        <div className="flow-root">
          <ul className="-mb-8">
            {steps.map((step, stepIdx) => {
              const formattedDate = formatDate(step.date);
              const isLast = stepIdx === steps.length - 1;

              return (
                <li key={step.id}>
                  <div className="relative pb-8">
                    {!isLast && (
                      <span
                        className={`absolute top-4 left-4 -ml-px h-full w-0.5 border-l-2 ${getConnectorColor(step.status)}`}
                        aria-hidden="true"
                      />
                    )}
                    <div className="relative flex space-x-3">
                      <div>
                        <span
                          className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${getStatusColor(step.status)}`}
                        >
                          {step.icon}
                        </span>
                      </div>
                      <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {step.title}
                          </p>
                          <p className="text-sm text-gray-500">
                            {step.description}
                          </p>
                          {step.status === 'current' && (
                            <p className="mt-1 text-xs text-blue-600 font-medium">
                              In Progress
                            </p>
                          )}
                        </div>
                        <div className="text-right text-sm whitespace-nowrap text-gray-500">
                          {formattedDate ? (
                            <div>
                              <p className="font-medium text-gray-900">{formattedDate.date}</p>
                              <p className="text-xs">{formattedDate.time}</p>
                            </div>
                          ) : step.status === 'upcoming' ? (
                            <p className="text-gray-400">Pending</p>
                          ) : (
                            <p className="text-gray-400">—</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              );
            })}
          </ul>
        </div>

        {/* Status Summary */}
        <div className="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-3">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 8h6m-6-4h6m8-8H2v16h20V7z" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">Reference</p>
                <p className="text-sm text-gray-600">{reimbursement.reference_number}</p>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">Amount</p>
                <p className="text-sm text-gray-600">KWD {reimbursement.amount.toFixed(3)}</p>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">Business</p>
                <p className="text-sm text-gray-600">{reimbursement.business.name}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Description */}
        {reimbursement.description && (
          <div className="mt-6">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Description</h4>
            <p className="text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
              {reimbursement.description}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};