# Subscription Tracker Web App

A comprehensive web application for tracking subscription payments and reimbursements across multiple businesses with multi-currency support (USD/GBP to KWD).

## Features

- **Business Management**: Manage multiple businesses with associated subscriptions
- **Subscription Tracking**: Track recurring subscriptions with automated payment calculations
- **Payment Recording**: Record payments with receipt uploads and currency conversion
- **Reimbursement Workflow**: Complete reimbursement tracking from request to completion
- **Multi-Currency Support**: Automatic conversion between USD/GBP and KWD
- **Analytics Dashboard**: Comprehensive reporting and analytics
- **Security**: Enterprise-grade security with authentication and data protection

## Tech Stack

### Frontend
- React 18 with TypeScript
- Tailwind CSS for styling
- React Query for state management
- React Hook Form with Zod validation
- Chart.js for data visualization

### Backend
- Node.js with Express.js and TypeScript
- Supabase for database and authentication
- Redis for caching
- File storage with Supabase Storage

## Project Structure

```
├── frontend/          # React frontend application
│   ├── src/
│   │   ├── components/    # Reusable UI components
│   │   ├── pages/         # Page components
│   │   ├── hooks/         # Custom React hooks
│   │   ├── utils/         # Utility functions
│   │   └── types/         # TypeScript type definitions
├── backend/           # Express.js backend API
│   ├── src/
│   │   ├── routes/        # API route handlers
│   │   ├── controllers/   # Business logic controllers
│   │   ├── models/        # Data models
│   │   ├── middleware/    # Express middleware
│   │   └── utils/         # Backend utilities
└── docs/              # Project documentation
```

## Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Git

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd subscription-tracker
```

2. Install dependencies
```bash
npm install
```

3. Setup environment variables
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Start development servers
```bash
npm run dev
```

## Development

- `npm run dev` - Start both frontend and backend in development mode
- `npm run build` - Build for production
- `npm run lint` - Run ESLint
- `npm run test` - Run tests

## License

MIT License