import { Router } from 'express';
import { z } from 'zod';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import { AuthService } from '../services/authService';

const router = Router();

// Validation schemas
const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters')
});

const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required')
});

const resetPasswordSchema = z.object({
  email: z.string().email('Invalid email format')
});

const updatePasswordSchema = z.object({
  password: z.string().min(8, 'Password must be at least 8 characters')
});

const updateProfileSchema = z.object({
  firstName: z.string().min(1, 'First name is required').optional(),
  lastName: z.string().min(1, 'Last name is required').optional(),
  email: z.string().email('Invalid email format').optional()
});

// POST /api/auth/register
router.post('/register', asyncHandler(async (req, res) => {
  const userData = registerSchema.parse(req.body);
  
  const result = await AuthService.register(userData);
  
  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    data: {
      user: {
        id: result.user.id,
        email: result.user.email
      },
      session: result.session
    }
  });
}));

// POST /api/auth/login
router.post('/login', asyncHandler(async (req, res) => {
  const loginData = loginSchema.parse(req.body);
  
  const result = await AuthService.login(loginData);
  
  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: {
        id: result.user.id,
        email: result.user.email
      },
      session: result.session
    }
  });
}));

// POST /api/auth/logout
router.post('/logout', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const token = req.headers.authorization?.split(' ')[1];
  
  if (token) {
    await AuthService.logout(token);
  }
  
  res.json({
    success: true,
    message: 'Logout successful'
  });
}));

// GET /api/auth/me
router.get('/me', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const userProfile = await AuthService.getUserProfile(userId);
  
  res.json({
    success: true,
    message: 'User profile retrieved successfully',
    data: userProfile
  });
}));

// PUT /api/auth/profile
router.put('/profile', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const updates = updateProfileSchema.parse(req.body);
  
  const updatedProfile = await AuthService.updateUserProfile(userId, {
    first_name: updates.firstName,
    last_name: updates.lastName,
    email: updates.email
  });
  
  res.json({
    success: true,
    message: 'Profile updated successfully',
    data: updatedProfile
  });
}));

// POST /api/auth/reset-password
router.post('/reset-password', asyncHandler(async (req, res) => {
  const { email } = resetPasswordSchema.parse(req.body);
  
  await AuthService.resetPassword(email);
  
  res.json({
    success: true,
    message: 'Password reset email sent'
  });
}));

// PUT /api/auth/update-password
router.put('/update-password', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { password } = updatePasswordSchema.parse(req.body);
  const token = req.headers.authorization?.split(' ')[1]!;
  
  await AuthService.updatePassword(token, password);
  
  res.json({
    success: true,
    message: 'Password updated successfully'
  });
}));

// PUT /api/auth/change-password
const changePasswordSchema = z.object({
  currentPassword: z.string().min(6, 'Current password is required'),
  newPassword: z.string().min(8, 'New password must be at least 8 characters')
});

router.put('/change-password', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { currentPassword, newPassword } = changePasswordSchema.parse(req.body);
  const userId = req.user!.id;
  
  await AuthService.changePassword(userId, currentPassword, newPassword);
  
  res.json({
    success: true,
    message: 'Password changed successfully'
  });
}));

// DELETE /api/auth/account
router.delete('/account', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  
  await AuthService.deleteAccount(userId);
  
  res.json({
    success: true,
    message: 'Account deleted successfully'
  });
}));

export { router as authRoutes };