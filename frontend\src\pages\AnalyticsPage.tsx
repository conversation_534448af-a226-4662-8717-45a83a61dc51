import React, { useState } from 'react';
import { DashboardCharts } from '../components/dashboard/DashboardCharts';
import { SectionHeader } from '../components/dashboard/DashboardComponents';
import { useBusiness } from '../contexts/BusinessContext';

export const AnalyticsPage: React.FC = () => {
  const { currentBusiness, businesses } = useBusiness();
  const [timeRange, setTimeRange] = useState<'3m' | '6m' | '12m'>('6m');

  const timeRangeOptions = [
    { value: '3m', label: '3 Months' },
    { value: '6m', label: '6 Months' },
    { value: '12m', label: '12 Months' }
  ];

  if (!currentBusiness) {
    return (
      <div className="space-y-6">
        <SectionHeader
          title="Analytics Dashboard"
          subtitle="Comprehensive business analytics and insights"
        />
        
        <div className="bg-white shadow rounded-lg p-12 text-center">
          <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Business Selected</h3>
          <p className="text-gray-500 mb-6">
            Please select a business from the sidebar to view detailed analytics and charts.
          </p>
          <p className="text-sm text-gray-400">
            {businesses.length} business{businesses.length !== 1 ? 'es' : ''} available
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <SectionHeader
            title="Analytics Dashboard"
            subtitle={`Comprehensive analytics for ${currentBusiness.name}`}
          />
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-4">
          {/* Time Range Selector */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">Time Range:</label>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as '3m' | '6m' | '12m')}
              className="block w-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              {timeRangeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          
          {/* Export Button */}
          <button
            onClick={() => {
              // TODO: Implement export functionality
              alert('Export functionality will be implemented in the next task');
            }}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export Data
          </button>
        </div>
      </div>

      {/* Business Summary */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {currentBusiness.reimbursement_timeline || 30}
            </div>
            <div className="text-sm text-gray-500">Days Reimbursement Timeline</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {new Date(currentBusiness.created_at).toLocaleDateString()}
            </div>
            <div className="text-sm text-gray-500">Business Created</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              Active
            </div>
            <div className="text-sm text-gray-500">Current Status</div>
          </div>
        </div>
      </div>

      {/* Analytics Warning */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">Analytics Information</h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>
                Charts show data based on the selected time range. Historical data may be limited for recently created businesses. 
                All amounts are displayed in Kuwaiti Dinar (KWD).
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Charts */}
      <DashboardCharts />

      {/* Additional Insights */}
      <div className="bg-white shadow rounded-lg p-6">
        <SectionHeader
          title="Business Intelligence Recommendations"
          subtitle="AI-powered insights and recommendations"
          className="mb-6"
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg">
            <div className="flex items-center mb-3">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
              <h4 className="ml-2 text-lg font-medium text-blue-900">Cost Optimization</h4>
            </div>
            <p className="text-sm text-blue-700">
              Review unused subscriptions and consolidate similar services to reduce monthly spending.
            </p>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg">
            <div className="flex items-center mb-3">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h4 className="ml-2 text-lg font-medium text-green-900">Payment Efficiency</h4>
            </div>
            <p className="text-sm text-green-700">
              Set up automated payment reminders to reduce processing delays and improve cash flow.
            </p>
          </div>

          <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-lg">
            <div className="flex items-center mb-3">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h4 className="ml-2 text-lg font-medium text-purple-900">Budget Planning</h4>
            </div>
            <p className="text-sm text-purple-700">
              Use historical data to create accurate budget forecasts and identify spending patterns.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};