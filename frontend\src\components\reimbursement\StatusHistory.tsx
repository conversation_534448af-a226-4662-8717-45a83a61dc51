import React, { useState, useEffect } from 'react';
import { useReimbursement, ReimbursementStatusChange } from '../../contexts/ReimbursementContext';

interface StatusHistoryProps {
  reimbursementId: string;
  className?: string;
}

export const StatusHistory: React.FC<StatusHistoryProps> = ({
  reimbursementId,
  className = ''
}) => {
  const { getReimbursementHistory } = useReimbursement();
  const [history, setHistory] = useState<ReimbursementStatusChange[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchHistory = async () => {
      try {
        setLoading(true);
        setError(null);
        const historyData = await getReimbursementHistory(reimbursementId);
        setHistory(historyData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch status history');
      } finally {
        setLoading(false);
      }
    };

    if (reimbursementId) {
      fetchHistory();
    }
  }, [reimbursementId, getReimbursementHistory]);

  const getStatusIcon = (fromStatus: string, toStatus: string) => {
    if (toStatus === 'approved') {
      return (
        <svg className="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
    } else if (toStatus === 'received') {
      return (
        <svg className="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      );
    } else if (toStatus === 'pending') {
      return (
        <svg className="h-5 w-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
    }
    
    return (
      <svg className="h-5 w-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
      </svg>
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-blue-100 text-blue-800';
      case 'received':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  const getChangeDescription = (fromStatus: string, toStatus: string) => {
    if (fromStatus === 'pending' && toStatus === 'approved') {
      return 'Request was approved for payment';
    } else if (fromStatus === 'approved' && toStatus === 'received') {
      return 'Payment was marked as received';
    } else if (toStatus === 'pending') {
      return 'Status was reverted to pending';
    }
    return `Status changed from ${fromStatus} to ${toStatus}`;
  };

  if (loading) {
    return (
      <div className={`bg-white ${className}`}>
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Status History</h3>
        </div>
        <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-sm text-gray-500">Loading history...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white ${className}`}>
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Status History</h3>
        </div>
        <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
          <div className="text-center">
            <svg className="mx-auto h-12 w-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="mt-2 text-sm text-red-600">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white ${className}`}>
      <div className="px-4 py-5 sm:px-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900">Status History</h3>
        <p className="mt-1 max-w-2xl text-sm text-gray-500">
          Track all status changes and actions taken on this reimbursement
        </p>
      </div>

      <div className="border-t border-gray-200">
        {history.length === 0 ? (
          <div className="px-4 py-8 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No status changes</h3>
            <p className="mt-1 text-sm text-gray-500">
              No status changes have been recorded for this reimbursement yet.
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {history.map((change, index) => {
              const formattedDate = formatDate(change.created_at);
              const isFirst = index === 0;

              return (
                <div key={change.id} className="px-4 py-4 sm:px-6">
                  <div className="flex items-start space-x-3">
                    {/* Icon */}
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-white border-2 border-gray-200 flex items-center justify-center">
                        {getStatusIcon(change.from_status, change.to_status)}
                      </div>
                    </div>

                    {/* Content */}
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span
                            className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getStatusColor(change.from_status)}`}
                          >
                            {change.from_status.charAt(0).toUpperCase() + change.from_status.slice(1)}
                          </span>
                          <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                          </svg>
                          <span
                            className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getStatusColor(change.to_status)}`}
                          >
                            {change.to_status.charAt(0).toUpperCase() + change.to_status.slice(1)}
                          </span>
                          {isFirst && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              Latest
                            </span>
                          )}
                        </div>
                        <div className="text-right text-sm text-gray-500">
                          <p className="font-medium">{formattedDate.date}</p>
                          <p className="text-xs">{formattedDate.time}</p>
                        </div>
                      </div>

                      <div className="mt-2">
                        <p className="text-sm text-gray-900">
                          {getChangeDescription(change.from_status, change.to_status)}
                        </p>
                        {change.notes && (
                          <div className="mt-2 p-3 bg-gray-50 rounded-md">
                            <p className="text-sm text-gray-700">
                              <span className="font-medium">Note:</span> {change.notes}
                            </p>
                          </div>
                        )}
                      </div>

                      {/* Changed by information */}
                      <div className="mt-2 flex items-center text-xs text-gray-500">
                        <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        <span>Changed by user {change.changed_by}</span>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Status Change Guidelines */}
      <div className="border-t border-gray-200 px-4 py-4 sm:px-6 bg-gray-50">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Status Guidelines</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-xs text-gray-600">
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
              Pending
            </span>
            <span>Awaiting approval</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
              Approved
            </span>
            <span>Ready for payment</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
              Received
            </span>
            <span>Payment completed</span>
          </div>
        </div>
      </div>
    </div>
  );
};