{"master": {"tasks": [{"id": 1, "title": "Setup Project Repository and Development Environment", "description": "Initialize the project with modern React/Node.js stack and configure development tools", "details": "Create project structure using Create React App with TypeScript template (v5.0.1) for frontend and Express.js (v4.18.2) with TypeScript for backend. Setup ESLint, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> for code quality. Configure package.json scripts for development, build, and deployment. Initialize Git repository with proper .gitignore files. Setup environment variables structure (.env.example files) for both frontend and backend. Create basic folder structure: frontend (src/components, src/pages, src/hooks, src/utils, src/types), backend (src/routes, src/controllers, src/models, src/middleware, src/utils). Install essential dependencies: React Query/TanStack Query for state management, Tailwind CSS for styling, React Hook Form for form handling, and Zod for validation.", "testStrategy": "Verify project builds successfully, linting passes, and all development scripts work. Test hot reload functionality and ensure TypeScript compilation is error-free.", "priority": "high", "dependencies": [], "status": "completed", "subtasks": [{"id": 1, "title": "Initialize Git Repository and Project Structure", "description": "Create the root project directory, initialize Git repository, and set up the basic folder structure for both frontend and backend applications", "dependencies": [], "details": "Create root directory, run 'git init', create frontend/ and backend/ directories. Set up .gitignore files for both Node.js and React applications. Create basic README.md structure. Initialize the folder hierarchy: frontend (src/components, src/pages, src/hooks, src/utils, src/types), backend (src/routes, src/controllers, src/models, src/middleware, src/utils).", "status": "pending", "testStrategy": "Verify directory structure exists and git repository is properly initialized"}, {"id": 2, "title": "Setup Frontend React Application with TypeScript", "description": "Initialize the React frontend using Create React App with TypeScript template and configure basic development environment", "dependencies": [1], "details": "Navigate to frontend directory, run 'npx create-react-app . --template typescript' (v5.0.1). Install additional dependencies: @tanstack/react-query for state management, tailwindcss for styling, react-hook-form for form handling, and zod for validation. Configure Tailwind CSS setup with tailwind.config.js and update src/index.css.", "status": "pending", "testStrategy": "Run 'npm start' to verify React app starts successfully with TypeScript compilation"}, {"id": 3, "title": "Setup Backend Express.js Application with TypeScript", "description": "Initialize the Node.js backend with Express.js and TypeScript configuration", "dependencies": [1], "details": "Navigate to backend directory, run 'npm init -y'. Install Express.js (v4.18.2), TypeScript, and related dependencies: express, @types/express, @types/node, typescript, ts-node, nodemon. Create tsconfig.json with appropriate compiler options. Create basic server.js entry point with Express app setup. Configure package.json scripts for development (nodemon) and build (tsc).", "status": "pending", "testStrategy": "Run 'npm run dev' to verify Express server starts and responds to basic HTTP requests"}, {"id": 4, "title": "Configure Development Tools and Code Quality", "description": "Set up <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> for code quality and pre-commit hooks across both frontend and backend", "dependencies": [2, 3], "details": "Install ESLint and <PERSON><PERSON>er in both frontend and backend directories. Configure .eslintrc.js files with TypeScript and React rules for frontend, Node.js rules for backend. Create .prettierrc files with consistent formatting rules. Install and configure <PERSON><PERSON> for pre-commit hooks. Set up lint-staged to run ESL<PERSON> and <PERSON><PERSON><PERSON> on staged files. Create shared configuration files at root level where possible.", "status": "pending", "testStrategy": "Test pre-commit hooks by making a commit with intentional formatting issues to verify they are caught and fixed"}, {"id": 5, "title": "Configure Environment Variables and Development Scripts", "description": "Set up environment variable structure and configure package.json scripts for development workflow", "dependencies": [2, 3, 4], "details": "Create .env.example files in both frontend and backend directories with sample environment variables (API URLs, database connections, JWT secrets, etc.). Add .env to .gitignore files. Configure package.json scripts at root level for concurrent development (using concurrently package): 'dev' to run both frontend and backend, 'build' for production builds, 'lint' for code quality checks. Set up CORS configuration in backend for frontend development server.", "status": "pending", "testStrategy": "Run 'npm run dev' from root to verify both frontend and backend start concurrently and can communicate with each other"}]}, {"id": 2, "title": "Configure Supabase Database and Authentication", "description": "Setup Supabase project with database schema and authentication configuration", "details": "Create Supabase project and configure database schema with all required tables: Users, Businesses, Subscriptions, Payments, and Reimbursements. Enable Row Level Security (RLS) policies for data protection. Setup authentication using Supabase Auth with email/password. Configure database relationships and foreign key constraints. Create indexes for performance optimization on frequently queried columns (user_id, business_id, payment_date). Install Supabase client library (@supabase/supabase-js v2.38.0) and configure environment variables. Setup database migrations using Supabase CLI. Create seed data for testing purposes. Configure real-time subscriptions for live updates if needed.", "testStrategy": "Test database connections, verify all tables are created correctly, test authentication flows, and validate RLS policies work as expected. Run database migrations and rollbacks successfully.", "priority": "high", "dependencies": [1], "status": "completed", "subtasks": [{"id": 1, "title": "Create Supabase Project and Install Dependencies", "description": "Set up new Supabase project and install required client libraries", "dependencies": [], "details": "Create new Supabase project via dashboard, obtain project URL and anon key, install @supabase/supabase-js v2.38.0, create .env file with SUPABASE_URL and SUPABASE_ANON_KEY variables, initialize Supabase client in application", "status": "pending", "testStrategy": "Verify connection to Supabase by testing client initialization and basic API call"}, {"id": 2, "title": "Design and Create Database Schema", "description": "Create all required database tables with proper relationships and constraints", "dependencies": [1], "details": "Create tables for Users, Businesses, Subscriptions, Payments, and Reimbursements using Supabase SQL editor or CLI. Define foreign key relationships (business_id references businesses, user_id references users). Set up proper data types, constraints, and default values. Create junction tables if needed for many-to-many relationships", "status": "pending", "testStrategy": "Test table creation and relationships by inserting sample data and verifying foreign key constraints work correctly"}, {"id": 3, "title": "Configure Authentication and Row Level Security", "description": "Set up Supabase Auth with email/password and implement RLS policies", "dependencies": [2], "details": "Enable email/password authentication in Supabase Auth settings. Create RLS policies for each table to ensure users can only access their own data and related business data. Configure user profile creation triggers. Set up authentication flows in the application with sign up, sign in, and sign out functionality", "status": "pending", "testStrategy": "Test authentication flows and verify RLS policies by attempting to access data with different user accounts"}, {"id": 4, "title": "Create Performance Indexes and Optimization", "description": "Add database indexes for frequently queried columns and optimize performance", "dependencies": [2], "details": "Create indexes on user_id, business_id, and payment_date columns across relevant tables. Add composite indexes for common query patterns. Analyze query performance using Supabase dashboard. Configure database connection pooling if needed. Set up query optimization for complex joins", "status": "pending", "testStrategy": "Run performance tests on common queries and verify index usage through query execution plans"}, {"id": 5, "title": "Setup Database Migrations and Seed Data", "description": "Configure migration system and create test data for development", "dependencies": [3, 4], "details": "Install Supabase CLI and initialize migration system. Create migration files for all schema changes. Generate seed data for Users, Businesses, Subscriptions, Payments, and Reimbursements tables. Configure real-time subscriptions for live updates on critical tables. Set up migration deployment process", "status": "pending", "testStrategy": "Test migrations by running them on fresh database instance and verify seed data is properly inserted with correct relationships"}]}, {"id": 3, "title": "Implement Currency Exchange Rate Integration", "description": "Integrate real-time currency exchange API for USD/GBP to KWD conversion", "details": "Integrate with the exchange rate API from https://github.com/fawazahmed0/exchange-api.git for real-time USD/GBP to KWD conversion. Create a service module to handle API calls with proper error handling and fallback mechanisms. Implement daily rate fetching scheduled job using node-cron (v3.0.3) to store historical rates in database. Create exchange_rates table to store daily rates with date, from_currency, to_currency, and rate columns. Implement rate caching mechanism with Redis or in-memory cache for performance. Add manual rate override functionality for users. Create utility functions for currency conversion calculations with proper rounding to 3 decimal places. Handle API rate limits and implement retry logic with exponential backoff.", "testStrategy": "Test API integration with mock data, verify rate storage in database, test conversion calculations accuracy, and validate error handling for API failures. Test scheduled job execution and manual rate override functionality.", "priority": "high", "dependencies": [2], "status": "pending", "subtasks": [{"id": 1, "title": "Set up exchange rate database schema and API service foundation", "description": "Create the database table structure for storing exchange rates and set up the basic API service module with configuration", "dependencies": [], "details": "Create exchange_rates table with columns: id, date, from_currency, to_currency, rate, created_at, updated_at. Add proper indexes on date and currency pairs. Set up a new service module 'ExchangeRateService' with configuration for the fawazahmed0 exchange API. Include environment variables for API configuration and database connection setup.", "status": "pending", "testStrategy": "Unit tests for database schema creation and service module initialization"}, {"id": 2, "title": "Implement API integration with error handling and retry logic", "description": "Build the core API integration functionality to fetch exchange rates from the external API with comprehensive error handling", "dependencies": [1], "details": "Implement HTTP client to fetch rates from https://github.com/fawazahmed0/exchange-api.git endpoints. Add exponential backoff retry logic for failed requests with configurable max retries. Implement proper error handling for network issues, API downtime, and rate limiting. Add request timeout configuration and circuit breaker pattern for API resilience.", "status": "pending", "testStrategy": "Unit tests for API calls, error scenarios, and retry mechanisms. Integration tests with mock API responses"}, {"id": 3, "title": "Build caching mechanism and rate storage system", "description": "Implement caching layer for performance optimization and database storage for historical rates", "dependencies": [2], "details": "Set up Redis or in-memory cache (configurable) for storing current exchange rates with TTL. Implement cache-aside pattern for rate retrieval. Create database operations to store daily rates with proper upsert logic to handle duplicate dates. Add cache invalidation strategies and fallback to database when cache is unavailable.", "status": "pending", "testStrategy": "Unit tests for cache operations, database storage, and fallback mechanisms"}, {"id": 4, "title": "Create scheduled job for daily rate fetching", "description": "Implement automated daily fetching of exchange rates using node-cron scheduler", "dependencies": [3], "details": "Install and configure node-cron (v3.0.3) to schedule daily rate fetching at specified time. Create cron job that fetches USD/GBP to KWD rates and stores them in database. Add job logging and monitoring for successful/failed rate updates. Implement graceful shutdown handling for cron jobs and job overlap prevention.", "status": "pending", "testStrategy": "Unit tests for cron job setup and execution. Integration tests for daily rate fetching workflow"}, {"id": 5, "title": "Implement currency conversion utilities and manual override functionality", "description": "Create utility functions for currency conversion calculations and admin functionality for manual rate overrides", "dependencies": [4], "details": "Build utility functions for currency conversion with proper rounding to 3 decimal places. Implement manual rate override functionality with admin authentication. Create conversion calculation methods that use cached rates with fallback to database. Add validation for currency codes and conversion amounts. Implement audit logging for manual rate changes.", "status": "pending", "testStrategy": "Unit tests for conversion calculations, rounding precision, and manual override functionality. Integration tests for end-to-end conversion workflows"}]}, {"id": 4, "title": "Build Authentication System and User Management", "description": "Implement secure user authentication with session management and user profiles", "details": "Create authentication components using React Hook Form and Zod validation. Implement login/register forms with proper error handling and user feedback. Setup JWT token management with automatic refresh using React Query. Create protected route wrapper component for authenticated pages. Implement user profile management with ability to update personal information. Add password reset functionality using Supabase Auth. Create authentication context using React Context API for global state management. Implement logout functionality with proper session cleanup. Add loading states and error boundaries for authentication flows. Setup middleware for backend routes to verify authentication tokens.", "testStrategy": "Test login/register flows, verify token refresh works correctly, test protected routes redirect properly, and validate password reset functionality. Test session persistence across browser refreshes and proper cleanup on logout.", "priority": "high", "dependencies": [2], "status": "pending", "subtasks": [{"id": 1, "title": "Setup Authentication Context and JWT Token Management", "description": "Create the foundational authentication infrastructure with React Context API and JWT token handling", "dependencies": [], "details": "Create AuthContext using React Context API to manage global authentication state. Implement JWT token storage in localStorage/sessionStorage with automatic refresh logic using React Query. Setup token validation and expiration handling. Create custom hooks (useAuth, useToken) for accessing authentication state throughout the app. Implement automatic token refresh before expiration using React Query's background refetch capabilities.", "status": "pending", "testStrategy": "Unit tests for context providers, token validation functions, and custom hooks. Mock localStorage and test token refresh scenarios."}, {"id": 2, "title": "Build Login and Registration Forms with Validation", "description": "Create secure login and registration forms using React Hook Form and Zod validation", "dependencies": [1], "details": "Implement login form with email/password fields using React Hook Form for form state management. Create registration form with validation for email, password strength, and confirmation fields. Use Zod schemas for client-side validation with proper error messages. Integrate forms with Supabase Auth for backend authentication. Add loading states during form submission and proper error handling for authentication failures. Implement form accessibility with proper ARIA labels and keyboard navigation.", "status": "pending", "testStrategy": "Integration tests for form submission, validation error handling, and Supabase Auth integration. Test accessibility compliance and keyboard navigation."}, {"id": 3, "title": "Create Protected Route Wrapper and Authentication Guards", "description": "Implement route protection system to secure authenticated pages", "dependencies": [1], "details": "Create ProtectedRoute component that wraps authenticated pages and redirects unauthenticated users to login. Implement authentication guards that check token validity before rendering protected components. Add loading states while authentication status is being verified. Create different access levels (authenticated, admin, etc.) with role-based route protection. Implement automatic redirect to intended destination after successful login.", "status": "pending", "testStrategy": "Integration tests for route protection, redirection logic, and role-based access control. Test with expired tokens and unauthenticated users."}, {"id": 4, "title": "Implement User Profile Management and Password Reset", "description": "Build user profile editing functionality and password reset system", "dependencies": [1, 2], "details": "Create user profile page with form for updating personal information (name, email, avatar). Implement profile update functionality with optimistic updates using React Query. Add password reset functionality using Supabase Auth's reset password feature. Create password reset form with email input and confirmation flow. Implement profile image upload with file validation and compression. Add success/error notifications for profile updates and password reset requests.", "status": "pending", "testStrategy": "End-to-end tests for profile updates, password reset flow, and file upload functionality. Test form validation and error handling."}, {"id": 5, "title": "Add Logout Functionality and Error Boundaries", "description": "Implement secure logout system with proper session cleanup and error handling", "dependencies": [1, 3], "details": "Create logout functionality that clears JWT tokens, resets authentication context, and redirects to login page. Implement proper session cleanup including clearing React Query cache and localStorage. Add error boundaries specifically for authentication flows to handle unexpected errors gracefully. Create loading states for logout process and confirmation dialogs for sensitive actions. Implement automatic logout on token expiration with user notification. Add session timeout warnings before automatic logout.", "status": "pending", "testStrategy": "Integration tests for logout flow, session cleanup, and error boundary handling. Test automatic logout scenarios and session timeout warnings."}]}, {"id": 5, "title": "Create Business Management System", "description": "Build CRUD operations for managing multiple businesses with associated metadata", "details": "Create business management components with forms for adding, editing, and deleting businesses. Implement business list view with search and filtering capabilities. Build business selection dropdown component for use throughout the application. Create business detail page with associated subscriptions and payment history. Implement business context provider for current business selection. Add validation for business fields using Zod schemas. Create API endpoints for business CRUD operations with proper error handling. Implement business deletion with cascade handling for associated subscriptions and payments. Add business statistics dashboard showing subscription count, total payments, and reimbursement status.", "testStrategy": "Test CRUD operations for businesses, verify data validation works correctly, test business selection persists across page navigations, and validate cascade deletion works properly without data loss.", "priority": "medium", "dependencies": [4], "status": "pending", "subtasks": [{"id": 1, "title": "Create Business Data Models and API Endpoints", "description": "Set up the foundational business data structure with Zod schemas and implement CRUD API endpoints", "dependencies": [], "details": "Create business entity with fields like name, description, address, contact info, and metadata. Implement Zod validation schemas for business creation and updates. Build API endpoints for POST /businesses, GET /businesses, GET /businesses/:id, PUT /businesses/:id, and DELETE /businesses/:id. Include proper error handling, validation, and cascade deletion logic for associated subscriptions and payments.", "status": "pending", "testStrategy": "Unit tests for API endpoints, validation schema tests, and integration tests for CRUD operations"}, {"id": 2, "title": "Build Business Context Provider and State Management", "description": "Create React context for managing current business selection and global business state", "dependencies": [1], "details": "Implement BusinessContext with provider component that manages currently selected business, business list state, and business operations. Include methods for switching between businesses, loading business data, and managing business state across the application. Add hooks like useBusinessContext, useCurrentBusiness, and useBusinessOperations for easy access to business functionality.", "status": "pending", "testStrategy": "React Testing Library tests for context provider, custom hooks tests, and state management integration tests"}, {"id": 3, "title": "Create Business Forms and CRUD Components", "description": "Build React components for creating, editing, and deleting businesses with proper form validation", "dependencies": [2], "details": "Create BusinessForm component with fields for all business properties, integrated with Zod validation. Build AddBusinessModal and EditBusinessModal components. Implement BusinessDeleteDialog with confirmation and cascade warning. Create BusinessFormFields as reusable form components. Add form state management, error handling, and success feedback. Include proper loading states and form submission handling.", "status": "pending", "testStrategy": "Component tests for form validation, user interaction tests, and form submission tests with mock API calls"}, {"id": 4, "title": "Implement Business List View and Selection Components", "description": "Build business list display with search, filtering, and business selection dropdown functionality", "dependencies": [3], "details": "Create BusinessList component with table/card view of all businesses. Implement search functionality by business name and description. Add filtering by business status, location, or other metadata. Build BusinessSelector dropdown component for use throughout the application. Include pagination, sorting, and bulk actions. Add empty states and loading indicators. Implement business selection persistence in localStorage or session storage.", "status": "pending", "testStrategy": "Component tests for search and filter functionality, user interaction tests for selection, and integration tests with context provider"}, {"id": 5, "title": "Build Business Detail Page and Statistics Dashboard", "description": "Create comprehensive business detail view with associated data and statistics dashboard", "dependencies": [4], "details": "Build BusinessDetailPage showing complete business information, associated subscriptions list, payment history, and business statistics. Create BusinessStats component displaying subscription count, total payments, reimbursement status, and other key metrics. Implement data visualization with charts for payment trends and subscription analytics. Add quick actions for editing business, managing subscriptions, and viewing detailed reports. Include responsive design and proper loading states for all data fetching.", "status": "pending", "testStrategy": "Page component tests, data visualization tests, and integration tests with business context and API endpoints"}]}, {"id": 6, "title": "Develop Subscription Management Interface", "description": "Build comprehensive subscription tracking with scheduling and categorization features", "details": "Create subscription management interface with data table using TanStack Table (v8.10.0) for sorting, filtering, and pagination. Implement subscription form with fields for service name, provider, amount, currency, billing frequency, and category. Build subscription card component showing key information and quick actions. Create subscription detail page with payment history and upcoming payments. Implement subscription categories with custom categories support. Add subscription status management (active, inactive, cancelled). Create recurring payment calculation logic based on billing frequency. Implement subscription search and filtering by business, category, amount range, and status. Add bulk operations for managing multiple subscriptions. Create subscription dashboard with upcoming payments and renewal reminders.", "testStrategy": "Test subscription CRUD operations, verify recurring payment calculations, test filtering and search functionality, and validate subscription categorization works correctly. Test bulk operations and subscription status changes.", "priority": "medium", "dependencies": [5], "status": "pending", "subtasks": [{"id": 1, "title": "Create Core Subscription Data Models and Database Schema", "description": "Define and implement the foundational data structures for subscription management including subscription entity, category entity, and payment history tracking", "dependencies": [], "details": "Create TypeScript interfaces for Subscription, SubscriptionCategory, and PaymentHistory entities. Define database schema with proper relationships between subscriptions, categories, and payment records. Include fields for service name, provider, amount, currency, billing frequency (monthly, yearly, etc.), status (active, inactive, cancelled), next payment date, and category assignment. Implement database migrations and seed data for default categories.", "status": "pending", "testStrategy": "Unit tests for data model validation, database integration tests for CRUD operations, and schema migration tests"}, {"id": 2, "title": "Build Subscription Management Data Table with TanStack Table", "description": "Implement the main subscription listing interface using TanStack Table v8.10.0 with comprehensive sorting, filtering, and pagination capabilities", "dependencies": [1], "details": "Create a responsive data table component using TanStack Table v8.10.0 that displays all subscriptions with columns for service name, provider, amount, billing frequency, category, status, and next payment date. Implement client-side sorting for all columns, filtering by business, category, amount range, and status. Add pagination with configurable page sizes. Include bulk selection functionality for managing multiple subscriptions simultaneously. Create custom column filters and search functionality.", "status": "pending", "testStrategy": "Component testing for table rendering, filtering logic, sorting behavior, and pagination functionality. Integration tests for data loading and user interactions"}, {"id": 3, "title": "Develop Subscription Form and Card Components", "description": "Create form components for adding/editing subscriptions and display card components for quick subscription overview", "dependencies": [1], "details": "Build a comprehensive subscription form with validated fields for service name, provider, amount, currency selector, billing frequency dropdown, category selection (with custom category creation), and status management. Implement form validation with proper error handling. Create subscription card component displaying key information (service name, amount, next payment date, status) with quick action buttons for edit, delete, and status toggle. Include responsive design for mobile and desktop views.", "status": "pending", "testStrategy": "Form validation testing, component rendering tests, user interaction testing for form submission and card actions"}, {"id": 4, "title": "Implement Subscription Categories and Status Management", "description": "Build category management system with custom categories support and comprehensive subscription status handling", "dependencies": [1], "details": "Create category management interface allowing users to create, edit, and delete custom subscription categories. Implement predefined categories (Entertainment, Utilities, Software, etc.) with color coding and icons. Build status management system handling active, inactive, and cancelled states with proper state transitions. Include category assignment to subscriptions with filtering and grouping capabilities. Add category-based analytics and reporting features.", "status": "pending", "testStrategy": "Category CRUD operation tests, status transition validation, category assignment testing, and analytics accuracy verification"}, {"id": 5, "title": "Create Subscription Detail Page and Payment Calculations", "description": "Build detailed subscription view with payment history, upcoming payments, and recurring payment calculation logic", "dependencies": [1, 2, 3, 4], "details": "Develop detailed subscription page showing complete subscription information, payment history table with date, amount, and status columns, and upcoming payments calendar. Implement recurring payment calculation logic based on billing frequency (monthly, yearly, quarterly) with proper date handling for different billing cycles. Create subscription dashboard with upcoming payments widget, renewal reminders, and spending analytics. Include payment trend charts and subscription cost projections. Add notification system for upcoming renewals and payment failures.", "status": "pending", "testStrategy": "Payment calculation accuracy tests, dashboard component testing, notification system tests, and integration tests for payment history retrieval"}]}, {"id": 7, "title": "Implement Payment Recording and Tracking System", "description": "Build payment entry system with receipt upload and automatic currency conversion", "details": "Create payment recording form with subscription selection, amount entry, and automatic currency conversion. Implement file upload functionality for receipts using drag-and-drop interface with react-dropzone (v14.2.3). Setup file storage using Supabase Storage with proper file organization and security policies. Create payment history table with sorting, filtering, and export capabilities. Implement payment method tracking (credit card, bank transfer, etc.). Add payment notes and reference number fields. Create payment summary cards showing totals by currency and time period. Implement payment editing and deletion with proper audit trails. Add payment status indicators and overdue payment alerts. Create payment calendar view showing due dates and payment history.", "testStrategy": "Test payment recording with various currencies, verify file upload and storage works correctly, test currency conversion calculations, and validate payment history filtering and sorting. Test payment editing and deletion functionality.", "priority": "medium", "dependencies": [6, 3], "status": "pending", "subtasks": [{"id": 1, "title": "Setup Payment Database Schema and Models", "description": "Create database tables and models for payment tracking system including payments, payment methods, and file attachments", "dependencies": [], "details": "Create payment table with fields: id, subscription_id, amount, currency, payment_date, payment_method, status, reference_number, notes, created_at, updated_at. Create payment_methods table with predefined options. Create payment_attachments table for receipt files. Setup foreign key relationships and indexes. Create corresponding TypeScript interfaces and database migration files.", "status": "pending", "testStrategy": "Unit tests for model validation, database migration tests, and relationship integrity tests"}, {"id": 2, "title": "Implement Payment Recording Form with Currency Conversion", "description": "Build the main payment entry form with subscription selection, amount input, and automatic currency conversion functionality", "dependencies": [1], "details": "Create form component with subscription dropdown, amount input with currency selector, payment method selection, payment date picker, reference number field, and notes textarea. Integrate with currency conversion API for real-time rates. Add form validation using react-hook-form or similar. Implement automatic currency conversion when subscription currency differs from entered amount currency. Add payment method management.", "status": "pending", "testStrategy": "Component tests for form validation, integration tests for currency conversion API, and user interaction tests"}, {"id": 3, "title": "Build Receipt Upload System with Supabase Storage", "description": "Implement file upload functionality for receipts using drag-and-drop interface and Supabase Storage integration", "dependencies": [1], "details": "Install and configure react-dropzone v14.2.3 for drag-and-drop file uploads. Setup Supabase Storage bucket with proper folder structure (payments/receipts/year/month/). Implement file validation (file types, size limits). Create upload progress indicators and error handling. Setup storage security policies for authenticated users. Implement file preview and download functionality. Add multiple file support for single payment.", "status": "pending", "testStrategy": "Integration tests for file upload, storage policy tests, and error handling tests for various file scenarios"}, {"id": 4, "title": "Create Payment History Table with Filtering and Export", "description": "Build comprehensive payment history display with sorting, filtering, search, and export capabilities", "dependencies": [1, 2, 3], "details": "Create data table component with columns for payment date, subscription, amount, currency, payment method, status, and actions. Implement sorting by all columns. Add filtering by date range, subscription, payment method, and status. Create search functionality for reference numbers and notes. Implement pagination for large datasets. Add export functionality to CSV/Excel format. Include payment summary statistics above the table.", "status": "pending", "testStrategy": "Component tests for table functionality, integration tests for filtering and sorting, and export functionality tests"}, {"id": 5, "title": "Implement Payment Management Features and Calendar View", "description": "Add payment editing, deletion, status tracking, summary cards, and calendar view for comprehensive payment management", "dependencies": [1, 2, 3, 4], "details": "Create payment edit modal with all payment fields. Implement soft delete with audit trail logging. Add payment status management (pending, completed, failed, refunded). Create summary cards showing totals by currency and time period. Implement overdue payment alerts and status indicators. Build calendar view using a calendar library showing payment due dates and history. Add payment method statistics and trending analysis. Implement proper error handling and loading states throughout.", "status": "pending", "testStrategy": "End-to-end tests for complete payment workflow, unit tests for audit trail functionality, and integration tests for calendar view"}]}, {"id": 8, "title": "Build Reimbursement Tracking Module", "description": "Create comprehensive reimbursement workflow with status tracking and timeline management", "details": "Build reimbursement tracking interface with timeline view showing request, approval, and payment stages. Create reimbursement request form linking to payments with automatic amount calculation in KWD. Implement reimbursement status management (Pending, Approved, Received) with status change tracking. Build reimbursement dashboard showing pending amounts, overdue requests, and business-wise breakdown. Create reimbursement calendar view with expected and actual dates. Implement automatic calculation of expected reimbursement dates based on business timeline settings. Add reimbursement reference number generation and tracking. Create reimbursement summary reports by business and time period. Implement reimbursement alerts for overdue payments. Add bulk reimbursement processing capabilities.", "testStrategy": "Test reimbursement workflow from request to completion, verify status tracking works correctly, test timeline calculations, and validate reimbursement reporting accuracy. Test bulk processing and alert generation.", "priority": "medium", "dependencies": [7], "status": "pending", "subtasks": [{"id": 1, "title": "Create Reimbursement Data Models and Database Schema", "description": "Design and implement the core data structures for reimbursement tracking including entities, relationships, and database migrations", "dependencies": [], "details": "Create reimbursement entity with fields for reference number, amount (KWD), status (Pending/Approved/Received), request date, expected date, actual date, business association, and payment linking. Implement status change tracking entity to log all status transitions with timestamps and user information. Create database migrations and seed data. Set up model relationships with existing payment and business entities. Implement reference number generation logic using business-specific prefixes and sequential numbering.", "status": "pending", "testStrategy": "Unit tests for model validations, database constraint testing, reference number generation testing, and relationship integrity checks"}, {"id": 2, "title": "Build Reimbursement Request Form and Payment Integration", "description": "Create the user interface for submitting reimbursement requests with automatic payment linking and amount calculations", "dependencies": [1], "details": "Build responsive form with fields for business selection, payment selection (with search/filter), amount calculation (auto-populated from linked payments), description, and expected date calculation based on business timeline settings. Implement form validation for required fields and amount limits. Create payment search functionality with filters by date range, business, and status. Implement automatic expected date calculation using business-specific reimbursement timeline settings. Add file upload capability for receipts and supporting documents. Create form submission handling with proper error management and success feedback.", "status": "pending", "testStrategy": "Form validation testing, payment linking functionality tests, automatic calculation verification, and file upload testing"}, {"id": 3, "title": "Implement Status Management and Timeline Tracking", "description": "Build the status management system with timeline view showing request, approval, and payment stages", "dependencies": [1, 2], "details": "Create status update interface for authorized users to change reimbursement status. Implement timeline view component showing visual progress through request → approval → payment stages with dates and responsible parties. Build status change tracking with audit trail including timestamps, user information, and optional comments. Create automated status transitions based on business rules (e.g., auto-approve small amounts). Implement email notifications for status changes. Add status filtering and search capabilities. Create timeline export functionality for individual reimbursements.", "status": "pending", "testStrategy": "Status transition testing, timeline display verification, notification delivery testing, and audit trail accuracy checks"}, {"id": 4, "title": "Build Reimbursement Dashboard and Analytics", "description": "Create comprehensive dashboard showing pending amounts, overdue requests, and business-wise breakdown with summary statistics", "dependencies": [1, 2, 3], "details": "Build main dashboard with key metrics cards showing total pending amount, overdue count, and average processing time. Create business-wise breakdown table with expandable rows showing individual reimbursements. Implement charts for monthly trends, status distribution, and business comparison. Add filtering by date range, business, status, and amount range. Create overdue alerts system with configurable thresholds. Build summary reports with export functionality (PDF/Excel). Implement role-based dashboard views (employee vs manager vs admin). Add quick action buttons for common tasks (approve, bulk process, etc.).", "status": "pending", "testStrategy": "Dashboard data accuracy testing, filtering functionality verification, chart rendering tests, and export format validation"}, {"id": 5, "title": "Create Calendar View and Bulk Processing Features", "description": "Implement calendar interface for date management and bulk processing capabilities for efficient reimbursement handling", "dependencies": [1, 2, 3, 4], "details": "Build calendar view component showing expected and actual reimbursement dates with color-coded status indicators. Implement drag-and-drop functionality for rescheduling expected dates. Create bulk processing interface with multi-select capabilities for batch status updates, approvals, and payments. Add calendar filters by business, status, and amount range. Implement calendar export functionality (iCal format). Create bulk import feature for processing multiple reimbursements from CSV/Excel files. Add calendar notifications for upcoming due dates. Implement calendar integration with external calendar systems (Google Calendar, Outlook).", "status": "pending", "testStrategy": "Calendar rendering and interaction testing, bulk operation verification, date calculation accuracy testing, and import/export functionality validation"}]}, {"id": 9, "title": "Create Dashboard and Analytics Interface", "description": "Build comprehensive dashboard with key metrics, charts, and business insights", "details": "Create main dashboard with key performance indicators: total active subscriptions, upcoming payments, pending reimbursements, and monthly spending. Implement interactive charts using Chart.js (v4.4.0) or Recharts (v2.8.0) for payment trends, currency distribution, and business comparison. Build spending analytics with month-over-month comparisons and forecasting. Create subscription health dashboard showing payment success rates and overdue subscriptions. Implement business comparison charts showing relative spending and reimbursement efficiency. Add date range selectors for historical data analysis. Create export functionality for dashboard data in PDF and CSV formats. Implement real-time updates for dashboard metrics using React Query mutations. Add customizable dashboard widgets allowing users to prioritize their preferred metrics.", "testStrategy": "Test dashboard loading performance with large datasets, verify chart accuracy and interactivity, test export functionality, and validate real-time updates work correctly. Test dashboard responsiveness across different screen sizes.", "priority": "medium", "dependencies": [8], "status": "pending", "subtasks": [{"id": 1, "title": "Set up Dashboard Foundation and Layout", "description": "Create the main dashboard component structure with responsive grid layout and navigation", "dependencies": [], "details": "Create Dashboard.tsx component with responsive CSS Grid or Flexbox layout. Implement sidebar navigation with menu items for different dashboard sections. Set up routing for dashboard sub-pages. Create header component with user info and date range selector. Implement basic loading states and error boundaries. Set up the foundation for widget containers that will house the metrics and charts.", "status": "pending", "testStrategy": "Unit tests for layout components, responsive design tests, navigation functionality tests"}, {"id": 2, "title": "Implement Core Metrics and KPI Cards", "description": "Build key performance indicator cards displaying total active subscriptions, upcoming payments, pending reimbursements, and monthly spending", "dependencies": [1], "details": "Create MetricCard component with consistent styling and loading states. Implement data fetching hooks using React Query for subscription metrics, payment data, and reimbursement statistics. Add real-time update capabilities with automatic refresh intervals. Create calculation logic for monthly spending aggregation and trending indicators. Implement error handling for failed metric calculations with fallback displays.", "status": "pending", "testStrategy": "Unit tests for metric calculations, integration tests for data fetching, visual regression tests for card layouts"}, {"id": 3, "title": "Build Interactive Charts and Visualizations", "description": "Implement Chart.js or Recharts components for payment trends, currency distribution, and business comparison charts", "dependencies": [2], "details": "Choose and integrate Chart.js v4.4.0 or Recharts v2.8.0 based on project requirements. Create reusable chart components: LineChart for payment trends, PieChart for currency distribution, BarChart for business comparisons. Implement responsive chart sizing and mobile-optimized views. Add interactive features like tooltips, zoom, and click handlers. Create chart data transformation utilities to format API data for visualization. Implement chart legends and axis labels with proper formatting.", "status": "pending", "testStrategy": "Visual tests for chart rendering, interaction tests for hover/click events, data transformation unit tests"}, {"id": 4, "title": "Create Analytics Dashboard with Historical Data", "description": "Build spending analytics with month-over-month comparisons, forecasting, and subscription health monitoring", "dependencies": [3], "details": "Create AnalyticsDashboard component with tabbed interface for different analytics views. Implement date range picker component with preset options (last 30 days, quarter, year). Build comparison logic for month-over-month spending analysis with percentage change calculations. Create forecasting algorithms for future spending predictions based on historical trends. Implement subscription health metrics showing payment success rates and overdue subscription tracking. Add filter capabilities for business units and subscription types.", "status": "pending", "testStrategy": "Unit tests for comparison calculations, integration tests for date filtering, accuracy tests for forecasting logic"}, {"id": 5, "title": "Add Customization and Export Features", "description": "Implement customizable dashboard widgets and export functionality for PDF and CSV formats", "dependencies": [4], "details": "Create widget customization interface allowing users to drag-and-drop, resize, and hide/show dashboard components. Implement user preference storage using localStorage or backend API. Build export functionality using libraries like jsPDF for PDF generation and csv-writer for CSV exports. Create export modal with format selection and data range options. Implement print-friendly dashboard layouts with proper styling. Add real-time updates using React Query mutations for live data synchronization. Create settings panel for dashboard personalization options.", "status": "pending", "testStrategy": "E2E tests for widget customization, export functionality tests with file validation, localStorage persistence tests"}]}, {"id": 10, "title": "Implement Reporting and Export System", "description": "Build comprehensive reporting system with PDF/CSV export capabilities and automated scheduling", "details": "Create reporting module with monthly, quarterly, and annual report generation. Implement PDF report generation using jsPDF (v2.5.1) or Puppeteer (v21.4.0) with professional formatting including charts and tables. Build CSV/Excel export functionality using xlsx (v0.18.5) for all data types. Create customizable report templates allowing users to select specific metrics and date ranges. Implement automated report scheduling with email delivery using node-c<PERSON> and Nodemailer (v6.9.7). Build report history and management interface showing generated reports with download links. Add report sharing capabilities with secure links and expiration dates. Create business-specific reports and cross-business comparison reports. Implement report caching for performance optimization. Add report customization options for branding and formatting preferences.", "testStrategy": "Test report generation with various data sets, verify PDF formatting and chart inclusion, test CSV export data integrity, and validate automated scheduling works correctly. Test report sharing and access controls.", "priority": "medium", "dependencies": [9], "status": "pending", "subtasks": [{"id": 1, "title": "Set up Report Generation Core Infrastructure", "description": "Create the foundational reporting system architecture including database schemas, core models, and basic report generation framework", "dependencies": [], "details": "Install and configure required packages (jsPDF v2.5.1, xlsx v0.18.5, node-cron, Nodemailer v6.9.7). Create database tables for report configurations, report history, and report templates. Set up base Report class with common functionality for all report types. Create ReportService with methods for report generation, storage, and retrieval. Implement basic report metadata tracking including creation time, status, and file paths. Set up file storage structure for generated reports with proper organization by date and type.", "status": "pending", "testStrategy": "Unit tests for Report model and ReportService core methods. Integration tests for database operations and file storage."}, {"id": 2, "title": "Build PDF Report Generation with Charts and Tables", "description": "Implement PDF report generation using jsPDF with professional formatting, charts, and tables for various report types", "dependencies": [1], "details": "Create PDFReportGenerator class extending base Report functionality. Implement chart generation using Chart.js or similar library for integration with jsPDF. Build table formatting utilities for clean data presentation in PDFs. Create report templates for monthly, quarterly, and annual reports with consistent branding and layout. Implement dynamic content insertion for metrics, date ranges, and business-specific data. Add header/footer templates with logo placement and page numbering. Create helper functions for formatting currency, dates, and percentages in reports.", "status": "pending", "testStrategy": "Unit tests for PDF generation functions. Visual regression testing for report layouts. End-to-end tests generating sample reports."}, {"id": 3, "title": "Implement CSV/Excel Export Functionality", "description": "Build comprehensive data export system supporting CSV and Excel formats for all data types with customizable column selection", "dependencies": [1], "details": "Create ExportService class for handling various export formats. Implement CSV export functionality with proper escaping and formatting. Build Excel export using xlsx library with multiple worksheets for complex data. Create export templates for different data types (transactions, customers, inventory, etc.). Implement column selection and filtering options for exports. Add data transformation utilities for proper formatting in export files. Create batch export functionality for large datasets with progress tracking.", "status": "pending", "testStrategy": "Unit tests for export functions with various data types. Integration tests for large dataset exports. File format validation tests."}, {"id": 4, "title": "Create Report Scheduling and Email Delivery System", "description": "Implement automated report scheduling with email delivery using node-cron and customizable scheduling options", "dependencies": [2, 3], "details": "Set up node-cron for scheduled report generation. Create ScheduledReportService for managing recurring reports. Build email delivery system using Nodemailer with HTML templates for report notifications. Implement scheduling configuration UI allowing users to set frequency (daily, weekly, monthly, quarterly). Create email templates for different report types with professional formatting. Add recipient management for scheduled reports with role-based access. Implement retry logic for failed report generation or email delivery. Create notification system for successful/failed report deliveries.", "status": "pending", "testStrategy": "Unit tests for scheduling logic. Integration tests for email delivery. Mock tests for cron job execution."}, {"id": 5, "title": "Build Report Management Interface and Sharing System", "description": "Create comprehensive report management UI with history, sharing capabilities, and customization options", "dependencies": [1, 2, 3, 4], "details": "Build report history interface showing all generated reports with filters and search. Create report download functionality with secure file serving. Implement report sharing system with secure links and expiration dates. Build report template customization interface allowing users to modify layouts, select metrics, and adjust branding. Create report preview functionality before generation. Implement report caching system for performance optimization. Add report comparison features for analyzing trends across time periods. Create user preference management for default report settings and branding options.", "status": "pending", "testStrategy": "UI component tests for report management interface. Integration tests for sharing functionality. Security tests for report access controls."}]}, {"id": 11, "title": "Implement Security Hardening and Performance Optimization", "description": "Apply security best practices and optimize application performance for production deployment", "details": "Implement comprehensive security measures including input validation using Zod schemas, SQL injection prevention through parameterized queries, XSS protection with Content Security Policy headers. Setup rate limiting using express-rate-limit (v7.1.5) on all API endpoints. Implement secure file upload validation with file type checking and size limits. Setup HTTPS enforcement and security headers using helmet (v7.1.0). Implement data encryption for sensitive information using bcrypt (v5.1.1) for passwords. Add audit logging for sensitive operations and data changes. Optimize database queries with proper indexing and query optimization. Implement caching strategies using Redis for frequently accessed data. Add compression middleware for API responses. Setup performance monitoring using tools like New Relic or DataDog. Implement lazy loading for React components and code splitting for optimal bundle sizes.", "testStrategy": "Run security vulnerability scans, test rate limiting under load, verify all security headers are properly set, and validate encryption/decryption works correctly. Test application performance under stress and verify caching mechanisms work effectively.", "priority": "high", "dependencies": [10], "status": "pending", "subtasks": [{"id": 1, "title": "Implement Input Validation and SQL Injection Prevention", "description": "Setup comprehensive input validation using Zod schemas and implement parameterized queries to prevent SQL injection attacks", "dependencies": [], "details": "Install and configure <PERSON><PERSON> for request validation schemas. Create validation middleware for all API endpoints. Replace any direct SQL queries with parameterized queries using prepared statements. Implement request body, query parameter, and URL parameter validation. Add error handling for validation failures with appropriate HTTP status codes.", "status": "pending", "testStrategy": "Unit tests for validation schemas, integration tests for SQL injection attempts, test malformed input handling"}, {"id": 2, "title": "Configure Security Headers and XSS Protection", "description": "Implement Content Security Policy headers, setup Helmet for security headers, and configure HTTPS enforcement", "dependencies": [1], "details": "Install helmet (v7.1.0) and configure comprehensive security headers including CSP, HSTS, X-Frame-Options, and X-Content-Type-Options. Setup HTTPS enforcement middleware. Configure CSP policies to prevent XSS attacks. Implement secure cookie settings and session management. Add CORS configuration with proper origin validation.", "status": "pending", "testStrategy": "Security header validation tests, XSS attempt tests, HTTPS redirection tests"}, {"id": 3, "title": "Implement Rate Limiting and File Upload Security", "description": "Setup API rate limiting using express-rate-limit and implement secure file upload validation with type and size restrictions", "dependencies": [2], "details": "Install express-rate-limit (v7.1.5) and configure rate limiting for all API endpoints with different limits for different endpoint types. Implement file upload validation with MIME type checking, file size limits, and filename sanitization. Setup secure file storage location outside web root. Add virus scanning for uploaded files if possible.", "status": "pending", "testStrategy": "Rate limiting tests with multiple requests, file upload tests with various file types and sizes, malicious file upload prevention tests"}, {"id": 4, "title": "Setup Data Encryption and Audit Logging", "description": "Implement password hashing with bcrypt and setup comprehensive audit logging for sensitive operations", "dependencies": [3], "details": "Install bcrypt (v5.1.1) and implement secure password hashing with appropriate salt rounds. Setup audit logging middleware to track sensitive operations like login attempts, data modifications, and administrative actions. Implement log rotation and secure log storage. Add encryption for sensitive data at rest using appropriate encryption algorithms.", "status": "pending", "testStrategy": "Password hashing and verification tests, audit log verification tests, encryption/decryption tests"}, {"id": 5, "title": "Optimize Performance with Caching and Monitoring", "description": "Implement Redis caching, database query optimization, response compression, and performance monitoring setup", "dependencies": [4], "details": "Setup Redis for caching frequently accessed data with appropriate TTL values. Optimize database queries by adding proper indexes and analyzing query performance. Implement response compression middleware. Setup performance monitoring with tools like New Relic or DataDog. Implement React code splitting and lazy loading for optimal bundle sizes. Add API response caching headers.", "status": "pending", "testStrategy": "Cache hit/miss tests, database query performance tests, compression verification tests, performance monitoring validation"}]}], "metadata": {"created": "2025-07-06T00:41:22.561Z", "updated": "2025-07-06T00:41:22.561Z", "description": "Tasks for master context"}}}