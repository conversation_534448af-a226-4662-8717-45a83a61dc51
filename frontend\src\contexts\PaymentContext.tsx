import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';

export interface Payment {
  id: string;
  subscription_id: string;
  amount: number;
  currency: 'USD' | 'GBP' | 'KWD';
  payment_date: string;
  payment_method: string;
  reference_number: string | null;
  notes: string | null;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  created_at: string;
  updated_at: string;
}

export interface PaymentAttachment {
  id: string;
  payment_id: string;
  file_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  created_at: string;
}

export interface PaymentFilters {
  subscription_id?: string;
  business_id?: string;
  status?: 'pending' | 'completed' | 'failed' | 'refunded';
  currency?: 'USD' | 'GBP' | 'KWD';
  payment_method?: string;
  date_from?: string;
  date_to?: string;
}

export interface PaymentStats {
  total: number;
  completed: number;
  pending: number;
  failed: number;
  refunded: number;
  totalAmounts: Record<string, number>;
}

interface PaymentContextType {
  payments: Payment[];
  paymentMethods: string[];
  loading: boolean;
  error: string | null;
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
  fetchPayments: (filters?: PaymentFilters, limit?: number, offset?: number) => Promise<void>;
  createPayment: (paymentData: Omit<Payment, 'id' | 'created_at' | 'updated_at'>) => Promise<Payment>;
  updatePayment: (id: string, updates: Partial<Payment>) => Promise<Payment>;
  deletePayment: (id: string) => Promise<void>;
  searchPayments: (searchTerm: string, filters?: PaymentFilters) => Promise<Payment[]>;
  getBusinessStats: (businessId: string) => Promise<PaymentStats>;
  fetchPaymentMethods: () => Promise<void>;
  uploadAttachments: (paymentId: string, files: File[]) => Promise<PaymentAttachment[]>;
  getPaymentAttachments: (paymentId: string) => Promise<PaymentAttachment[]>;
}

const PaymentContext = createContext<PaymentContextType | undefined>(undefined);

export const usePayment = () => {
  const context = useContext(PaymentContext);
  if (context === undefined) {
    throw new Error('usePayment must be used within a PaymentProvider');
  }
  return context;
};

interface PaymentProviderProps {
  children: ReactNode;
}

export const PaymentProvider: React.FC<PaymentProviderProps> = ({ children }) => {
  const { user, session } = useAuth();
  const [payments, setPayments] = useState<Payment[]>([]);
  const [paymentMethods, setPaymentMethods] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    limit: 50,
    offset: 0,
    hasMore: false
  });

  const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

  const getAuthHeaders = () => ({
    'Authorization': `Bearer ${session?.access_token}`
  });

  const fetchPayments = async (
    filters: PaymentFilters = {},
    limit: number = 50,
    offset: number = 0
  ) => {
    if (!user || !session) return;

    setLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== undefined && value !== '')
        )
      });

      const response = await fetch(`${API_BASE_URL}/payments?${queryParams}`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to fetch payments');
      }

      const result = await response.json();
      setPayments(result.data);
      setPagination(result.pagination);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch payments');
    } finally {
      setLoading(false);
    }
  };

  const createPayment = async (
    paymentData: Omit<Payment, 'id' | 'created_at' | 'updated_at'>
  ): Promise<Payment> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/payments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        },
        body: JSON.stringify(paymentData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create payment');
      }

      const result = await response.json();
      const newPayment = result.data;

      setPayments(prev => [newPayment, ...prev]);
      return newPayment;
    } catch (err) {
      throw err;
    }
  };

  const updatePayment = async (id: string, updates: Partial<Payment>): Promise<Payment> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/payments/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        },
        body: JSON.stringify(updates)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update payment');
      }

      const result = await response.json();
      const updatedPayment = result.data;

      setPayments(prev => prev.map(p => p.id === id ? updatedPayment : p));
      return updatedPayment;
    } catch (err) {
      throw err;
    }
  };

  const deletePayment = async (id: string): Promise<void> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/payments/${id}`, {
        method: 'DELETE',
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete payment');
      }

      setPayments(prev => prev.filter(p => p.id !== id));
    } catch (err) {
      throw err;
    }
  };

  const searchPayments = async (
    searchTerm: string,
    filters: PaymentFilters = {}
  ): Promise<Payment[]> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const queryParams = new URLSearchParams({
        q: searchTerm,
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== undefined && value !== '')
        )
      });

      const response = await fetch(`${API_BASE_URL}/payments?${queryParams}`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to search payments');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      throw err instanceof Error ? err : new Error('Failed to search payments');
    }
  };

  const getBusinessStats = async (businessId: string): Promise<PaymentStats> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/payments/business/${businessId}/stats`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to fetch business statistics');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      throw err instanceof Error ? err : new Error('Failed to fetch business statistics');
    }
  };

  const fetchPaymentMethods = async () => {
    if (!user || !session) return;

    try {
      const response = await fetch(`${API_BASE_URL}/payments/methods`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to fetch payment methods');
      }

      const result = await response.json();
      setPaymentMethods(result.data);
    } catch (err) {
      console.error('Failed to fetch payment methods:', err);
    }
  };

  const uploadAttachments = async (paymentId: string, files: File[]): Promise<PaymentAttachment[]> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const formData = new FormData();
      files.forEach(file => {
        formData.append('files', file);
      });

      const response = await fetch(`${API_BASE_URL}/payments/${paymentId}/attachments`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to upload attachments');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      throw err instanceof Error ? err : new Error('Failed to upload attachments');
    }
  };

  const getPaymentAttachments = async (paymentId: string): Promise<PaymentAttachment[]> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/payments/${paymentId}/attachments`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to fetch payment attachments');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      throw err instanceof Error ? err : new Error('Failed to fetch payment attachments');
    }
  };

  // Fetch initial data when user logs in
  useEffect(() => {
    if (user && session) {
      fetchPayments();
      fetchPaymentMethods();
    } else {
      setPayments([]);
      setPaymentMethods([]);
    }
  }, [user, session]);

  const value: PaymentContextType = {
    payments,
    paymentMethods,
    loading,
    error,
    pagination,
    fetchPayments,
    createPayment,
    updatePayment,
    deletePayment,
    searchPayments,
    getBusinessStats,
    fetchPaymentMethods,
    uploadAttachments,
    getPaymentAttachments
  };

  return (
    <PaymentContext.Provider value={value}>
      {children}
    </PaymentContext.Provider>
  );
};