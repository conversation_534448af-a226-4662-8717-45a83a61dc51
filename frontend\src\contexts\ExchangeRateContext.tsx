
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';

export interface ExchangeRate {
  id: string;
  date: string;
  from_currency: string;
  to_currency: string;
  rate: number;
}

export interface CurrencyConversion {
  amount: number;
  fromCurrency: string;
  toCurrency: string;
  convertedAmount: number;
  rate: number;
  date: string;
}

interface ExchangeRateContextType {
  rates: ExchangeRate[];
  loading: boolean;
  error: string | null;
  fetchRates: (from?: string, to?: string) => Promise<void>;
  convertCurrency: (amount: number, from: string, to: string, date?: string) => Promise<CurrencyConversion | null>;
}

const ExchangeRateContext = createContext<ExchangeRateContextType | undefined>(undefined);

export const useExchangeRate = () => {
  const context = useContext(ExchangeRateContext);
  if (context === undefined) {
    throw new Error('useExchangeRate must be used within an ExchangeRateProvider');
  }
  return context;
};

interface ExchangeRateProviderProps {
  children: ReactNode;
}

export const ExchangeRateProvider: React.FC<ExchangeRateProviderProps> = ({ children }) => {
  const { session } = useAuth();
  const [rates, setRates] = useState<ExchangeRate[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

  const getAuthHeaders = () => ({
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${session?.access_token}`
  });

  const fetchRates = async (from: string = 'USD', to: string = 'KWD') => {
    if (!session) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_BASE_URL}/exchange-rates/historical?fromCurrency=${from}&toCurrency=${to}&startDate=2023-01-01&endDate=2025-12-31`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to fetch exchange rates');
      }

      const result = await response.json();
      setRates(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch exchange rates');
    } finally {
      setLoading(false);
    }
  };

  const convertCurrency = async (
    amount: number,
    from: string,
    to: string,
    date?: string
  ): Promise<CurrencyConversion | null> => {
    if (!session) return null;

    try {
      const response = await fetch(`${API_BASE_URL}/exchange-rates/convert`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({ amount, fromCurrency: from, toCurrency: to, date })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to convert currency');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      console.error(err);
      return null;
    }
  };

  useEffect(() => {
    if (session) {
      fetchRates();
    }
  }, [session]);

  const value: ExchangeRateContextType = {
    rates,
    loading,
    error,
    fetchRates,
    convertCurrency
  };

  return (
    <ExchangeRateContext.Provider value={value}>
      {children}
    </ExchangeRateContext.Provider>
  );
};
