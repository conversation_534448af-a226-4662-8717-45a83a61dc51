import React, { useState, useRef, useEffect } from 'react';
import { Business, useBusiness } from '../../contexts/BusinessContext';

interface BusinessSelectorProps {
  onBusinessChange?: (business: Business | null) => void;
  className?: string;
}

export const BusinessSelector: React.FC<BusinessSelectorProps> = ({
  onBusinessChange,
  className = ''
}) => {
  const { businesses, currentBusiness, setCurrentBusiness } = useBusiness();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleBusinessSelect = (business: Business) => {
    setCurrentBusiness(business);
    onBusinessChange?.(business);
    setIsOpen(false);
  };

  if (businesses.length === 0) {
    return (
      <div className={`text-sm text-gray-500 ${className}`}>
        No businesses available
      </div>
    );
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full bg-white border border-gray-300 rounded-md px-3 py-2 text-left cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      >
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            {currentBusiness ? (
              <div>
                <p className="text-sm font-medium text-gray-900 truncate">
                  {currentBusiness.name}
                </p>
                {currentBusiness.description && (
                  <p className="text-xs text-gray-500 truncate">
                    {currentBusiness.description}
                  </p>
                )}
              </div>
            ) : (
              <p className="text-sm text-gray-500">Select a business</p>
            )}
          </div>
          <svg
            className={`h-5 w-5 text-gray-400 transition-transform ${
              isOpen ? 'transform rotate-180' : ''
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </button>

      {isOpen && (
        <div className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none">
          {businesses.map((business) => (
            <button
              key={business.id}
              onClick={() => handleBusinessSelect(business)}
              className={`w-full text-left px-4 py-2 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 ${
                currentBusiness?.id === business.id ? 'bg-blue-50 text-blue-900' : 'text-gray-900'
              }`}
            >
              <div>
                <p className="font-medium truncate">{business.name}</p>
                {business.description && (
                  <p className="text-sm text-gray-500 truncate">{business.description}</p>
                )}
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};