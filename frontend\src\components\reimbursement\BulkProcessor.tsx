import React, { useState } from 'react';
import { Reimbursement, useReimbursement } from '../../contexts/ReimbursementContext';

interface BulkProcessorProps {
  reimbursements: Reimbursement[];
  selectedIds: string[];
  onSelectionChange: (ids: string[]) => void;
  onComplete?: () => void;
  className?: string;
}

interface BulkAction {
  id: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  requiresConfirmation: boolean;
  allowedStatuses: string[];
}

export const BulkProcessor: React.FC<BulkProcessorProps> = ({
  reimbursements,
  selectedIds,
  onSelectionChange,
  onComplete,
  className = ''
}) => {
  const { updateReimbursementStatus, deleteReimbursement } = useReimbursement();
  const [isProcessing, setIsProcessing] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [selectedAction, setSelectedAction] = useState<BulkAction | null>(null);
  const [processingNotes, setProcessingNotes] = useState('');
  const [results, setResults] = useState<{ success: number; failed: number; errors: string[] }>({
    success: 0,
    failed: 0,
    errors: []
  });

  const selectedReimbursements = reimbursements.filter(r => selectedIds.includes(r.id));

  const bulkActions: BulkAction[] = [
    {
      id: 'approve',
      label: 'Approve Selected',
      description: 'Mark selected pending reimbursements as approved',
      icon: (
        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      color: 'bg-blue-600 hover:bg-blue-700',
      requiresConfirmation: true,
      allowedStatuses: ['pending']
    },
    {
      id: 'mark-received',
      label: 'Mark as Received',
      description: 'Mark selected approved reimbursements as received',
      icon: (
        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      ),
      color: 'bg-green-600 hover:bg-green-700',
      requiresConfirmation: true,
      allowedStatuses: ['approved']
    },
    {
      id: 'revert-pending',
      label: 'Revert to Pending',
      description: 'Revert selected approved reimbursements back to pending',
      icon: (
        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      color: 'bg-yellow-600 hover:bg-yellow-700',
      requiresConfirmation: true,
      allowedStatuses: ['approved']
    },
    {
      id: 'delete',
      label: 'Delete Selected',
      description: 'Delete selected pending reimbursements (cannot be undone)',
      icon: (
        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
      ),
      color: 'bg-red-600 hover:bg-red-700',
      requiresConfirmation: true,
      allowedStatuses: ['pending']
    }
  ];

  const handleSelectAll = () => {
    if (selectedIds.length === reimbursements.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(reimbursements.map(r => r.id));
    }
  };

  const handleSelectByStatus = (status: string) => {
    const statusReimbursements = reimbursements.filter(r => r.status === status);
    const statusIds = statusReimbursements.map(r => r.id);
    
    // If all items of this status are already selected, deselect them
    const allStatusSelected = statusIds.every(id => selectedIds.includes(id));
    
    if (allStatusSelected) {
      onSelectionChange(selectedIds.filter(id => !statusIds.includes(id)));
    } else {
      // Add all status items to selection
      const newSelection = [...new Set([...selectedIds, ...statusIds])];
      onSelectionChange(newSelection);
    }
  };

  const getActionEligibleCount = (action: BulkAction) => {
    return selectedReimbursements.filter(r => 
      action.allowedStatuses.includes(r.status)
    ).length;
  };

  const handleActionClick = (action: BulkAction) => {
    const eligibleCount = getActionEligibleCount(action);
    
    if (eligibleCount === 0) {
      alert(`No reimbursements are eligible for this action. This action requires status: ${action.allowedStatuses.join(' or ')}`);
      return;
    }

    setSelectedAction(action);
    if (action.requiresConfirmation) {
      setShowConfirmation(true);
    } else {
      executeAction(action);
    }
  };

  const executeAction = async (action: BulkAction) => {
    if (!action) return;

    setIsProcessing(true);
    setResults({ success: 0, failed: 0, errors: [] });

    const eligibleReimbursements = selectedReimbursements.filter(r => 
      action.allowedStatuses.includes(r.status)
    );

    let successCount = 0;
    let failedCount = 0;
    const errors: string[] = [];

    for (const reimbursement of eligibleReimbursements) {
      try {
        switch (action.id) {
          case 'approve':
            await updateReimbursementStatus(reimbursement.id, 'approved', processingNotes || undefined);
            break;
          case 'mark-received':
            await updateReimbursementStatus(reimbursement.id, 'received', processingNotes || undefined);
            break;
          case 'revert-pending':
            await updateReimbursementStatus(reimbursement.id, 'pending', processingNotes || undefined);
            break;
          case 'delete':
            await deleteReimbursement(reimbursement.id);
            break;
          default:
            throw new Error('Unknown action');
        }
        successCount++;
      } catch (error) {
        failedCount++;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push(`${reimbursement.reference_number}: ${errorMessage}`);
      }
    }

    setResults({ success: successCount, failed: failedCount, errors });
    setIsProcessing(false);
    setShowConfirmation(false);
    setSelectedAction(null);
    setProcessingNotes('');

    // Clear selection and notify parent
    onSelectionChange([]);
    onComplete?.();
  };

  const cancelAction = () => {
    setShowConfirmation(false);
    setSelectedAction(null);
    setProcessingNotes('');
  };

  const formatCurrency = (amount: number) => `KWD ${amount.toFixed(3)}`;

  const totalSelectedAmount = selectedReimbursements.reduce((sum, r) => sum + r.amount, 0);

  return (
    <div className={`bg-white border rounded-lg ${className}`}>
      {/* Header */}
      <div className="px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Bulk Operations</h3>
          {selectedIds.length > 0 && (
            <span className="text-sm text-gray-500">
              {selectedIds.length} selected • Total: {formatCurrency(totalSelectedAmount)}
            </span>
          )}
        </div>
      </div>

      {/* Selection Controls */}
      <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
        <div className="flex flex-wrap items-center gap-2">
          <button
            onClick={handleSelectAll}
            className="inline-flex items-center px-3 py-1 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            {selectedIds.length === reimbursements.length ? 'Deselect All' : 'Select All'}
            <span className="ml-1 text-gray-500">({reimbursements.length})</span>
          </button>
          
          <button
            onClick={() => handleSelectByStatus('pending')}
            className="inline-flex items-center px-3 py-1 text-sm font-medium text-yellow-700 bg-yellow-100 border border-yellow-300 rounded-md hover:bg-yellow-200"
          >
            Pending
            <span className="ml-1 text-yellow-600">
              ({reimbursements.filter(r => r.status === 'pending').length})
            </span>
          </button>
          
          <button
            onClick={() => handleSelectByStatus('approved')}
            className="inline-flex items-center px-3 py-1 text-sm font-medium text-blue-700 bg-blue-100 border border-blue-300 rounded-md hover:bg-blue-200"
          >
            Approved
            <span className="ml-1 text-blue-600">
              ({reimbursements.filter(r => r.status === 'approved').length})
            </span>
          </button>
          
          <button
            onClick={() => handleSelectByStatus('received')}
            className="inline-flex items-center px-3 py-1 text-sm font-medium text-green-700 bg-green-100 border border-green-300 rounded-md hover:bg-green-200"
          >
            Received
            <span className="ml-1 text-green-600">
              ({reimbursements.filter(r => r.status === 'received').length})
            </span>
          </button>
        </div>
      </div>

      {/* Bulk Actions */}
      <div className="px-4 py-3">
        {selectedIds.length === 0 ? (
          <div className="text-center py-6 text-gray-500">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <p className="mt-2 text-sm">Select reimbursements to perform bulk operations</p>
          </div>
        ) : (
          <div className="space-y-3">
            <p className="text-sm text-gray-600 mb-3">
              Choose an action to perform on {selectedIds.length} selected reimbursement(s):
            </p>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {bulkActions.map(action => {
                const eligibleCount = getActionEligibleCount(action);
                const isDisabled = eligibleCount === 0;
                
                return (
                  <button
                    key={action.id}
                    onClick={() => handleActionClick(action)}
                    disabled={isDisabled || isProcessing}
                    className={`
                      flex items-center justify-between p-3 rounded-md border text-left transition-colors
                      ${isDisabled 
                        ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed' 
                        : `${action.color} text-white border-transparent hover:shadow-md`
                      }
                      ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}
                    `}
                  >
                    <div className="flex items-center">
                      {action.icon}
                      <div className="ml-3">
                        <div className="text-sm font-medium">{action.label}</div>
                        <div className="text-xs opacity-90">{action.description}</div>
                      </div>
                    </div>
                    <div className="text-sm font-medium">
                      {eligibleCount}/{selectedIds.length}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Results Display */}
      {(results.success > 0 || results.failed > 0) && (
        <div className="px-4 py-3 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {results.success > 0 && (
                <div className="flex items-center text-green-600">
                  <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-sm font-medium">{results.success} successful</span>
                </div>
              )}
              {results.failed > 0 && (
                <div className="flex items-center text-red-600">
                  <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-sm font-medium">{results.failed} failed</span>
                </div>
              )}
            </div>
            {results.errors.length > 0 && (
              <button
                onClick={() => alert(results.errors.join('\n'))}
                className="text-sm text-gray-600 hover:text-gray-800"
              >
                View Errors
              </button>
            )}
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmation && selectedAction && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Confirm Bulk Action
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to <strong>{selectedAction.label.toLowerCase()}</strong>?
                      </p>
                      <p className="text-sm text-gray-500 mt-1">
                        This will affect <strong>{getActionEligibleCount(selectedAction)}</strong> reimbursement(s).
                      </p>
                      
                      <div className="mt-4">
                        <label htmlFor="bulk-notes" className="block text-sm font-medium text-gray-700">
                          Notes (optional)
                        </label>
                        <textarea
                          id="bulk-notes"
                          rows={3}
                          value={processingNotes}
                          onChange={(e) => setProcessingNotes(e.target.value)}
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Add a note about this bulk operation..."
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={() => executeAction(selectedAction)}
                  disabled={isProcessing}
                  className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm ${
                    selectedAction.color
                  } ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {isProcessing ? 'Processing...' : 'Confirm'}
                </button>
                <button
                  type="button"
                  onClick={cancelAction}
                  disabled={isProcessing}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};