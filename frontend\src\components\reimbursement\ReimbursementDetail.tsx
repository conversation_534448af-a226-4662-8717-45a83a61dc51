import React, { useState } from 'react';
import { ReimbursementTimeline } from './ReimbursementTimeline';
import { StatusHistory } from './StatusHistory';
import { Reimbursement, useReimbursement } from '../../contexts/ReimbursementContext';

interface ReimbursementDetailProps {
  reimbursement: Reimbursement;
  onClose: () => void;
  onEdit?: () => void;
  className?: string;
}

export const ReimbursementDetail: React.FC<ReimbursementDetailProps> = ({
  reimbursement,
  onClose,
  onEdit,
  className = ''
}) => {
  const { updateReimbursementStatus } = useReimbursement();
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [statusNotes, setStatusNotes] = useState('');
  const [showStatusForm, setShowStatusForm] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<'pending' | 'approved' | 'received'>('pending');

  const formatCurrency = (amount: number) => {
    return `KWD ${amount.toFixed(3)}`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  const getValidStatusTransitions = (currentStatus: string) => {
    const transitions: Record<string, { status: string; label: string; color: string }[]> = {
      pending: [
        { status: 'approved', label: 'Approve', color: 'bg-blue-600 hover:bg-blue-700' }
      ],
      approved: [
        { status: 'received', label: 'Mark as Received', color: 'bg-green-600 hover:bg-green-700' },
        { status: 'pending', label: 'Revert to Pending', color: 'bg-yellow-600 hover:bg-yellow-700' }
      ],
      received: []
    };
    
    return transitions[currentStatus] || [];
  };

  const handleStatusUpdate = async (newStatus: 'pending' | 'approved' | 'received') => {
    setSelectedStatus(newStatus);
    setShowStatusForm(true);
  };

  const confirmStatusUpdate = async () => {
    if (selectedStatus === reimbursement.status) {
      setShowStatusForm(false);
      return;
    }

    setIsUpdatingStatus(true);
    try {
      await updateReimbursementStatus(
        reimbursement.id,
        selectedStatus,
        statusNotes.trim() || undefined
      );
      setShowStatusForm(false);
      setStatusNotes('');
    } catch (error) {
      console.error('Failed to update status:', error);
      alert('Failed to update status. Please try again.');
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const cancelStatusUpdate = () => {
    setShowStatusForm(false);
    setStatusNotes('');
    setSelectedStatus('pending');
  };

  const validTransitions = getValidStatusTransitions(reimbursement.status);
  const isOverdue = reimbursement.status !== 'received' && new Date(reimbursement.expected_date) < new Date();

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      {/* Header */}
      <div className="bg-white shadow rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Reimbursement Details
              </h1>
              <p className="mt-1 text-sm text-gray-500">
                Reference: {reimbursement.reference_number}
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <span
                className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  reimbursement.status === 'pending'
                    ? 'bg-yellow-100 text-yellow-800'
                    : reimbursement.status === 'approved'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-green-100 text-green-800'
                }`}
              >
                {reimbursement.status.charAt(0).toUpperCase() + reimbursement.status.slice(1)}
              </span>
              {isOverdue && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                  Overdue
                </span>
              )}
              <button
                onClick={onClose}
                className="rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-4 flex items-center space-x-3">
            {validTransitions.map((transition) => (
              <button
                key={transition.status}
                onClick={() => handleStatusUpdate(transition.status as any)}
                className={`inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white ${transition.color} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
              >
                {transition.label}
              </button>
            ))}
            {onEdit && reimbursement.status === 'pending' && (
              <button
                onClick={onEdit}
                className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Edit
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Status Update Form */}
      {showStatusForm && (
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Update Status to "{selectedStatus.charAt(0).toUpperCase() + selectedStatus.slice(1)}"
            </h3>
            <div className="mt-4">
              <label htmlFor="status-notes" className="block text-sm font-medium text-gray-700">
                Notes (optional)
              </label>
              <textarea
                id="status-notes"
                rows={3}
                value={statusNotes}
                onChange={(e) => setStatusNotes(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Add a note about this status change..."
              />
            </div>
            <div className="mt-4 flex items-center space-x-3">
              <button
                onClick={confirmStatusUpdate}
                disabled={isUpdatingStatus}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {isUpdatingStatus ? 'Updating...' : 'Confirm'}
              </button>
              <button
                onClick={cancelStatusUpdate}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Timeline */}
        <div className="lg:col-span-1">
          <ReimbursementTimeline
            reimbursement={reimbursement}
            className="shadow rounded-lg"
          />
        </div>

        {/* Status History */}
        <div className="lg:col-span-1">
          <StatusHistory
            reimbursementId={reimbursement.id}
            className="shadow rounded-lg"
          />
        </div>
      </div>

      {/* Payment Details */}
      <div className="mt-6 bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Associated Payments ({reimbursement.payments.length})
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Payments included in this reimbursement request
          </p>
        </div>
        <div className="border-t border-gray-200">
          <div className="overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Service
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {reimbursement.payments.map((payment) => {
                  const paymentDate = formatDate(payment.payment_date);
                  return (
                    <tr key={payment.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {payment.subscription.service_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {payment.subscription.provider}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {payment.currency} {payment.amount.toFixed(2)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {paymentDate.date}
                        </div>
                        <div className="text-sm text-gray-500">
                          {paymentDate.time}
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
          <div className="bg-gray-50 px-6 py-3">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-900">Total Amount:</span>
              <span className="text-lg font-bold text-gray-900">
                {formatCurrency(reimbursement.amount)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Information */}
      <div className="mt-6 bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Additional Information
          </h3>
        </div>
        <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
          <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt className="text-sm font-medium text-gray-500">Request Date</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {formatDate(reimbursement.request_date).date} at {formatDate(reimbursement.request_date).time}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Expected Date</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {formatDate(reimbursement.expected_date).date}
                {isOverdue && (
                  <span className="ml-2 text-sm text-red-600 font-medium">(Overdue)</span>
                )}
              </dd>
            </div>
            {reimbursement.actual_date && (
              <div>
                <dt className="text-sm font-medium text-gray-500">Received Date</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {formatDate(reimbursement.actual_date).date} at {formatDate(reimbursement.actual_date).time}
                </dd>
              </div>
            )}
            <div>
              <dt className="text-sm font-medium text-gray-500">Business Timeline</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {reimbursement.business.reimbursement_timeline} days
              </dd>
            </div>
            <div className="sm:col-span-2">
              <dt className="text-sm font-medium text-gray-500">Business</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {reimbursement.business.name}
              </dd>
            </div>
            {reimbursement.description && (
              <div className="sm:col-span-2">
                <dt className="text-sm font-medium text-gray-500">Description</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {reimbursement.description}
                </dd>
              </div>
            )}
          </dl>
        </div>
      </div>
    </div>
  );
};