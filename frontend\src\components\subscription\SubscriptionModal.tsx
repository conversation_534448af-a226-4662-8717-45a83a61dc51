import React from 'react';
import { Subscription } from '../../contexts/SubscriptionContext';
import { SubscriptionForm } from './SubscriptionForm';

interface SubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  subscription?: Subscription;
  onSubmit: (data: any) => Promise<void>;
  isLoading?: boolean;
  title: string;
  categories?: string[];
}

export const SubscriptionModal: React.FC<SubscriptionModalProps> = ({
  isOpen,
  onClose,
  subscription,
  onSubmit,
  isLoading = false,
  title,
  categories = []
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        ></div>

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  {title}
                </h3>
                <SubscriptionForm
                  subscription={subscription}
                  onSubmit={onSubmit}
                  onCancel={onClose}
                  isLoading={isLoading}
                  categories={categories}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};