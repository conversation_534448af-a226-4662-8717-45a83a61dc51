import React, { useMemo } from 'react';
import { <PERSON>Chart, BarChart, DonutChart, AreaChart, ChartDataPoint, TimeSeriesDataPoint } from './Charts';
import { ChartContainer } from './DashboardComponents';
import { useBusiness } from '../../contexts/BusinessContext';
import { useSubscription } from '../../contexts/SubscriptionContext';
import { usePayment } from '../../contexts/PaymentContext';
import { useReimbursement } from '../../contexts/ReimbursementContext';

export const DashboardCharts: React.FC = () => {
  const { currentBusiness } = useBusiness();
  const { subscriptions, loading: subscriptionsLoading } = useSubscription();
  const { payments, loading: paymentsLoading } = usePayment();
  const { reimbursements, loading: reimbursementsLoading } = useReimbursement();

  const isLoading = subscriptionsLoading || paymentsLoading || reimbursementsLoading;

  // Generate spending trend data for the last 6 months
  const spendingTrendData = useMemo((): TimeSeriesDataPoint[] => {
    if (!payments.length) return [];

    const last6Months = Array.from({ length: 6 }, (_, i) => {
      const date = new Date();
      date.setMonth(date.getMonth() - (5 - i));
      return {
        date: date.toISOString(),
        month: date.getMonth(),
        year: date.getFullYear()
      };
    });

    return last6Months.map(({ date, month, year }) => {
      const monthlySpend = payments
        .filter(p => {
          const paymentDate = new Date(p.payment_date);
          return p.status === 'completed' &&
                 paymentDate.getMonth() === month &&
                 paymentDate.getFullYear() === year;
        })
        .reduce((sum, p) => sum + p.amount, 0);

      return {
        date,
        value: monthlySpend,
        label: new Date(date).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
      };
    });
  }, [payments]);

  // Subscription category breakdown
  const subscriptionCategoryData = useMemo((): ChartDataPoint[] => {
    if (!subscriptions.length) return [];

    const categoryMap = new Map<string, number>();
    
    subscriptions
      .filter(s => s.status === 'active')
      .forEach(sub => {
        const category = sub.category || 'Other';
        categoryMap.set(category, (categoryMap.get(category) || 0) + sub.amount);
      });

    return Array.from(categoryMap.entries()).map(([label, value]) => ({
      label,
      value: Math.round(value * 100) / 100
    }));
  }, [subscriptions]);

  // Payment frequency by month
  const paymentFrequencyData = useMemo((): ChartDataPoint[] => {
    if (!payments.length) return [];

    const last6Months = Array.from({ length: 6 }, (_, i) => {
      const date = new Date();
      date.setMonth(date.getMonth() - (5 - i));
      return {
        month: date.getMonth(),
        year: date.getFullYear(),
        label: date.toLocaleDateString('en-US', { month: 'short' })
      };
    });

    return last6Months.map(({ month, year, label }) => {
      const count = payments.filter(p => {
        const paymentDate = new Date(p.payment_date);
        return p.status === 'completed' &&
               paymentDate.getMonth() === month &&
               paymentDate.getFullYear() === year;
      }).length;

      return { label, value: count };
    });
  }, [payments]);

  // Reimbursement status distribution
  const reimbursementStatusData = useMemo((): ChartDataPoint[] => {
    if (!reimbursements.length) return [];

    const statusMap = new Map([
      ['pending', 0],
      ['approved', 0],
      ['received', 0]
    ]);

    reimbursements.forEach(r => {
      statusMap.set(r.status, (statusMap.get(r.status) || 0) + 1);
    });

    const colors = {
      pending: '#F59E0B',
      approved: '#3B82F6',
      received: '#10B981'
    };

    return Array.from(statusMap.entries())
      .filter(([_, value]) => value > 0)
      .map(([label, value]) => ({
        label: label.charAt(0).toUpperCase() + label.slice(1),
        value,
        color: colors[label as keyof typeof colors]
      }));
  }, [reimbursements]);

  // Monthly reimbursement trend
  const reimbursementTrendData = useMemo((): TimeSeriesDataPoint[] => {
    if (!reimbursements.length) return [];

    const last6Months = Array.from({ length: 6 }, (_, i) => {
      const date = new Date();
      date.setMonth(date.getMonth() - (5 - i));
      return {
        date: date.toISOString(),
        month: date.getMonth(),
        year: date.getFullYear()
      };
    });

    return last6Months.map(({ date, month, year }) => {
      const monthlyAmount = reimbursements
        .filter(r => {
          const requestDate = new Date(r.request_date);
          return requestDate.getMonth() === month &&
                 requestDate.getFullYear() === year;
        })
        .reduce((sum, r) => sum + r.amount, 0);

      return {
        date,
        value: monthlyAmount
      };
    });
  }, [reimbursements]);

  // Subscription growth over time
  const subscriptionGrowthData = useMemo((): TimeSeriesDataPoint[] => {
    if (!subscriptions.length) return [];

    const last6Months = Array.from({ length: 6 }, (_, i) => {
      const date = new Date();
      date.setMonth(date.getMonth() - (5 - i));
      return {
        date: date.toISOString(),
        endOfMonth: new Date(date.getFullYear(), date.getMonth() + 1, 0)
      };
    });

    return last6Months.map(({ date, endOfMonth }) => {
      const activeCount = subscriptions.filter(s => {
        const createdDate = new Date(s.created_at);
        const cancelledDate = s.cancelled_at ? new Date(s.cancelled_at) : null;
        
        return createdDate <= endOfMonth && 
               (!cancelledDate || cancelledDate > endOfMonth);
      }).length;

      return {
        date,
        value: activeCount
      };
    });
  }, [subscriptions]);

  if (!currentBusiness) {
    return (
      <div className="space-y-6">
        <ChartContainer
          title="Analytics Dashboard"
          subtitle="Select a business to view charts and analytics"
          className="text-center p-12"
        >
          <div className="text-gray-500">
            Please select a business from the sidebar to view detailed analytics and charts.
          </div>
        </ChartContainer>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Financial Overview Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartContainer
          title="Spending Trend"
          subtitle="Monthly spending over the last 6 months"
          loading={isLoading}
        >
          {spendingTrendData.length > 0 ? (
            <AreaChart
              data={spendingTrendData}
              width={400}
              height={200}
              color="#10B981"
              className="w-full"
            />
          ) : (
            <div className="flex items-center justify-center h-48 text-gray-500">
              No spending data available
            </div>
          )}
        </ChartContainer>

        <ChartContainer
          title="Subscription Categories"
          subtitle="Active subscriptions by category"
          loading={isLoading}
        >
          {subscriptionCategoryData.length > 0 ? (
            <DonutChart
              data={subscriptionCategoryData}
              size={250}
              className="flex flex-col items-center"
            />
          ) : (
            <div className="flex items-center justify-center h-48 text-gray-500">
              No subscription data available
            </div>
          )}
        </ChartContainer>
      </div>

      {/* Performance Metrics Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartContainer
          title="Payment Activity"
          subtitle="Number of payments processed monthly"
          loading={isLoading}
        >
          {paymentFrequencyData.length > 0 ? (
            <BarChart
              data={paymentFrequencyData}
              width={400}
              height={200}
              color="#3B82F6"
              className="w-full"
            />
          ) : (
            <div className="flex items-center justify-center h-48 text-gray-500">
              No payment data available
            </div>
          )}
        </ChartContainer>

        <ChartContainer
          title="Subscription Growth"
          subtitle="Total active subscriptions over time"
          loading={isLoading}
        >
          {subscriptionGrowthData.length > 0 ? (
            <LineChart
              data={subscriptionGrowthData}
              width={400}
              height={200}
              color="#8B5CF6"
              className="w-full"
            />
          ) : (
            <div className="flex items-center justify-center h-48 text-gray-500">
              No subscription data available
            </div>
          )}
        </ChartContainer>
      </div>

      {/* Reimbursement Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartContainer
          title="Reimbursement Status"
          subtitle="Current status distribution"
          loading={isLoading}
        >
          {reimbursementStatusData.length > 0 ? (
            <DonutChart
              data={reimbursementStatusData}
              size={250}
              className="flex flex-col items-center"
            />
          ) : (
            <div className="flex items-center justify-center h-48 text-gray-500">
              No reimbursement data available
            </div>
          )}
        </ChartContainer>

        <ChartContainer
          title="Reimbursement Trends"
          subtitle="Monthly reimbursement amounts"
          loading={isLoading}
        >
          {reimbursementTrendData.length > 0 ? (
            <AreaChart
              data={reimbursementTrendData}
              width={400}
              height={200}
              color="#F59E0B"
              fillOpacity={0.2}
              className="w-full"
            />
          ) : (
            <div className="flex items-center justify-center h-48 text-gray-500">
              No reimbursement data available
            </div>
          )}
        </ChartContainer>
      </div>

      {/* Summary Insights */}
      <ChartContainer
        title="Business Insights"
        subtitle="Key observations and recommendations"
        loading={isLoading}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
          {spendingTrendData.length >= 2 && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Spending Trend</h4>
              <p className="text-sm text-blue-700">
                {spendingTrendData[spendingTrendData.length - 1].value > spendingTrendData[spendingTrendData.length - 2].value
                  ? '📈 Spending increased this month'
                  : '📉 Spending decreased this month'
                }
              </p>
            </div>
          )}
          
          {subscriptionCategoryData.length > 0 && (
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-medium text-green-900 mb-2">Top Category</h4>
              <p className="text-sm text-green-700">
                💼 {subscriptionCategoryData.reduce((max, item) => 
                  item.value > max.value ? item : max
                ).label} is your largest expense
              </p>
            </div>
          )}
          
          {reimbursementStatusData.length > 0 && (
            <div className="bg-yellow-50 p-4 rounded-lg">
              <h4 className="font-medium text-yellow-900 mb-2">Reimbursements</h4>
              <p className="text-sm text-yellow-700">
                {reimbursementStatusData.find(item => item.label === 'Pending')?.value || 0 > 0
                  ? '⏳ Pending reimbursements need attention'
                  : '✅ All reimbursements are processed'
                }
              </p>
            </div>
          )}
        </div>
      </ChartContainer>
    </div>
  );
};