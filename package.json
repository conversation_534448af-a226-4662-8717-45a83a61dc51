{"name": "subscription-tracker", "version": "1.0.0", "description": "A comprehensive subscription tracking application with multi-currency support", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm start", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "start": "cd backend && npm start"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["subscription", "tracker", "react", "express", "typescript", "supabase"], "author": "", "license": "MIT", "workspaces": ["frontend", "backend"]}