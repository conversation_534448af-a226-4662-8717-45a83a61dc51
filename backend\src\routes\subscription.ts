import { Router } from 'express';
import { z } from 'zod';
import { asyncHandler } from '../middleware/errorHandler';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import { SubscriptionService } from '../services/subscriptionService';

const router = Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Validation schemas
const subscriptionSchema = z.object({
  business_id: z.string().uuid('Invalid business ID'),
  service_name: z.string().min(1, 'Service name is required').max(100, 'Service name too long'),
  provider: z.string().min(1, 'Provider is required').max(100, 'Provider name too long'),
  amount: z.number().positive('Amount must be positive').max(999999.99, 'Amount too large'),
  currency: z.enum(['USD', 'GBP', 'KWD']),
  billing_frequency: z.enum(['monthly', 'quarterly', 'yearly']),
  category: z.string().max(50, 'Category name too long').optional(),
  status: z.enum(['active', 'inactive', 'cancelled']).default('active'),
  next_payment_date: z.string().datetime('Invalid date format')
});

const updateSubscriptionSchema = subscriptionSchema.partial();

const filtersSchema = z.object({
  business_id: z.string().uuid().optional(),
  status: z.enum(['active', 'inactive', 'cancelled']).optional(),
  category: z.string().optional(),
  currency: z.enum(['USD', 'GBP', 'KWD']).optional(),
  provider: z.string().optional(),
  limit: z.string().transform(val => parseInt(val)).pipe(z.number().min(1).max(100)).optional(),
  offset: z.string().transform(val => parseInt(val)).pipe(z.number().min(0)).optional(),
  q: z.string().min(1).optional()
});

const bulkUpdateSchema = z.object({
  ids: z.array(z.string().uuid()),
  updates: updateSubscriptionSchema
});

const bulkDeleteSchema = z.object({
  ids: z.array(z.string().uuid())
});

const categorySchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Color must be a valid hex color'),
  icon: z.string().optional(),
  description: z.string().optional()
});

// GET /api/subscriptions
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const filters = filtersSchema.parse(req.query);
  
  const { q, limit = 50, offset = 0, ...subscriptionFilters } = filters;

  let result;
  if (q) {
    const subscriptions = await SubscriptionService.searchSubscriptions(userId, q, subscriptionFilters);
    result = { subscriptions, total: subscriptions.length };
  } else {
    result = await SubscriptionService.getUserSubscriptions(userId, subscriptionFilters, limit, offset);
  }

  res.json({
    success: true,
    message: 'Subscriptions retrieved successfully',
    data: result.subscriptions,
    pagination: {
      total: result.total,
      limit,
      offset,
      hasMore: offset + limit < result.total
    }
  });
}));

// GET /api/subscriptions/categories
router.get('/categories', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const categories = await SubscriptionService.getSubscriptionCategories(userId);

  res.json({
    success: true,
    message: 'Subscription categories retrieved successfully',
    data: categories
  });
}));

// GET /api/subscriptions/upcoming
router.get('/upcoming', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const { days } = z.object({
    days: z.string().transform(val => parseInt(val)).pipe(z.number().min(1).max(365)).optional()
  }).parse(req.query);

  const upcomingPayments = await SubscriptionService.getUpcomingPayments(userId, days);

  res.json({
    success: true,
    message: 'Upcoming payments retrieved successfully',
    data: upcomingPayments
  });
}));

// GET /api/subscriptions/:id
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const userId = req.user!.id;
  
  const subscription = await SubscriptionService.getSubscriptionById(id, userId);
  
  res.json({
    success: true,
    message: 'Subscription retrieved successfully',
    data: subscription
  });
}));

// POST /api/subscriptions
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const subscriptionData = subscriptionSchema.parse(req.body);
  
  const subscription = await SubscriptionService.createSubscription(userId, subscriptionData);
  
  res.status(201).json({
    success: true,
    message: 'Subscription created successfully',
    data: subscription
  });
}));

// PUT /api/subscriptions/:id
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const updates = updateSubscriptionSchema.parse(req.body);
  
  const subscription = await SubscriptionService.updateSubscription(id, userId, updates);
  
  res.json({
    success: true,
    message: 'Subscription updated successfully',
    data: subscription
  });
}));

// DELETE /api/subscriptions/:id
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const userId = req.user!.id;
  
  await SubscriptionService.deleteSubscription(id, userId);
  
  res.json({
    success: true,
    message: 'Subscription deleted successfully'
  });
}));

// GET /api/subscriptions/business/:businessId/stats
router.get('/business/:businessId/stats', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { businessId } = req.params;
  const userId = req.user!.id;

  const stats = await SubscriptionService.getBusinessSubscriptionStats(businessId, userId);

  res.json({
    success: true,
    message: 'Business subscription statistics retrieved successfully',
    data: stats
  });
}));

// PUT /api/subscriptions/bulk-update
router.put('/bulk-update', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { ids, updates } = bulkUpdateSchema.parse(req.body);
  const userId = req.user!.id;

  const updatedSubscriptions = await SubscriptionService.bulkUpdateSubscriptions(ids, updates, userId);

  res.json({
    success: true,
    message: 'Subscriptions updated successfully',
    data: updatedSubscriptions
  });
}));

// DELETE /api/subscriptions/bulk-delete
router.delete('/bulk-delete', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { ids } = bulkDeleteSchema.parse(req.body);
  const userId = req.user!.id;

  await SubscriptionService.bulkDeleteSubscriptions(ids, userId);

  res.json({
    success: true,
    message: 'Subscriptions deleted successfully'
  });
}));

// GET /api/subscriptions/categories
router.get('/categories', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;

  const categories = await SubscriptionService.getCategories(userId);

  res.json({
    success: true,
    message: 'Categories retrieved successfully',
    data: categories
  });
}));

// POST /api/subscriptions/categories
router.post('/categories', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const categoryData = categorySchema.parse(req.body);
  const userId = req.user!.id;

  const category = await SubscriptionService.createCategory(categoryData, userId);

  res.json({
    success: true,
    message: 'Category created successfully',
    data: category
  });
}));

// PUT /api/subscriptions/categories/:id
router.put('/categories/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const updates = categorySchema.partial().parse(req.body);
  const userId = req.user!.id;

  const category = await SubscriptionService.updateCategory(id, updates, userId);

  res.json({
    success: true,
    message: 'Category updated successfully',
    data: category
  });
}));

// DELETE /api/subscriptions/categories/:id
router.delete('/categories/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const userId = req.user!.id;

  await SubscriptionService.deleteCategory(id, userId);

  res.json({
    success: true,
    message: 'Category deleted successfully'
  });
}));

export { router as subscriptionRoutes };