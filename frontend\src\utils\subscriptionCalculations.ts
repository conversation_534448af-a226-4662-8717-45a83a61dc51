import { Subscription } from '../contexts/SubscriptionContext';

export interface PaymentCalculation {
  nextPaymentDate: Date;
  monthlyAmount: number;
  yearlyAmount: number;
  daysUntilPayment: number;
  isOverdue: boolean;
  isUpcoming: boolean; // within next 7 days
}

export interface SubscriptionAnalytics {
  totalMonthlySpending: number;
  totalYearlySpending: number;
  averageSubscriptionCost: number;
  mostExpensiveSubscription: Subscription | null;
  upcomingPayments: Subscription[];
  overduePayments: Subscription[];
  spendingByCategory: Record<string, number>;
  spendingByCurrency: Record<string, number>;
  spendingByFrequency: Record<string, number>;
  subscriptionTrends: {
    month: string;
    count: number;
    totalAmount: number;
  }[];
  costOptimizationSuggestions: string[];
}

export class SubscriptionCalculations {
  /**
   * Calculate next payment date based on current date and billing frequency
   */
  static calculateNextPaymentDate(subscription: Subscription): Date {
    const currentDate = new Date(subscription.next_payment_date);
    const nextDate = new Date(currentDate);

    switch (subscription.billing_frequency) {
      case 'monthly':
        nextDate.setMonth(nextDate.getMonth() + 1);
        break;
      case 'quarterly':
        nextDate.setMonth(nextDate.getMonth() + 3);
        break;
      case 'yearly':
        nextDate.setFullYear(nextDate.getFullYear() + 1);
        break;
    }

    return nextDate;
  }

  /**
   * Convert subscription amount to monthly equivalent
   */
  static getMonthlyAmount(subscription: Subscription): number {
    switch (subscription.billing_frequency) {
      case 'monthly':
        return subscription.amount;
      case 'quarterly':
        return subscription.amount / 3;
      case 'yearly':
        return subscription.amount / 12;
      default:
        return subscription.amount;
    }
  }

  /**
   * Convert subscription amount to yearly equivalent
   */
  static getYearlyAmount(subscription: Subscription): number {
    switch (subscription.billing_frequency) {
      case 'monthly':
        return subscription.amount * 12;
      case 'quarterly':
        return subscription.amount * 4;
      case 'yearly':
        return subscription.amount;
      default:
        return subscription.amount * 12;
    }
  }

  /**
   * Get payment calculation details for a subscription
   */
  static getPaymentCalculation(subscription: Subscription): PaymentCalculation {
    const nextPaymentDate = new Date(subscription.next_payment_date);
    const today = new Date();
    const daysUntilPayment = Math.ceil((nextPaymentDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    return {
      nextPaymentDate,
      monthlyAmount: this.getMonthlyAmount(subscription),
      yearlyAmount: this.getYearlyAmount(subscription),
      daysUntilPayment,
      isOverdue: daysUntilPayment < 0,
      isUpcoming: daysUntilPayment >= 0 && daysUntilPayment <= 7,
    };
  }

  /**
   * Get upcoming payments within specified days
   */
  static getUpcomingPayments(subscriptions: Subscription[], days: number = 30): Subscription[] {
    const today = new Date();
    const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);

    return subscriptions
      .filter(sub => {
        if (sub.status !== 'active') return false;
        const paymentDate = new Date(sub.next_payment_date);
        return paymentDate >= today && paymentDate <= futureDate;
      })
      .sort((a, b) => new Date(a.next_payment_date).getTime() - new Date(b.next_payment_date).getTime());
  }

  /**
   * Get overdue payments
   */
  static getOverduePayments(subscriptions: Subscription[]): Subscription[] {
    const today = new Date();
    
    return subscriptions
      .filter(sub => {
        if (sub.status !== 'active') return false;
        const paymentDate = new Date(sub.next_payment_date);
        return paymentDate < today;
      })
      .sort((a, b) => new Date(a.next_payment_date).getTime() - new Date(b.next_payment_date).getTime());
  }

  /**
   * Calculate total monthly spending
   */
  static getTotalMonthlySpending(subscriptions: Subscription[]): number {
    return subscriptions
      .filter(sub => sub.status === 'active')
      .reduce((total, sub) => total + this.getMonthlyAmount(sub), 0);
  }

  /**
   * Calculate total yearly spending
   */
  static getTotalYearlySpending(subscriptions: Subscription[]): number {
    return subscriptions
      .filter(sub => sub.status === 'active')
      .reduce((total, sub) => total + this.getYearlyAmount(sub), 0);
  }

  /**
   * Get spending breakdown by category
   */
  static getSpendingByCategory(subscriptions: Subscription[]): Record<string, number> {
    const categorySpending: Record<string, number> = {};
    
    subscriptions
      .filter(sub => sub.status === 'active')
      .forEach(sub => {
        const category = sub.category || 'Other';
        const monthlyAmount = this.getMonthlyAmount(sub);
        categorySpending[category] = (categorySpending[category] || 0) + monthlyAmount;
      });
    
    return categorySpending;
  }

  /**
   * Get spending breakdown by currency
   */
  static getSpendingByCurrency(subscriptions: Subscription[]): Record<string, number> {
    const currencySpending: Record<string, number> = {};
    
    subscriptions
      .filter(sub => sub.status === 'active')
      .forEach(sub => {
        currencySpending[sub.currency] = (currencySpending[sub.currency] || 0) + sub.amount;
      });
    
    return currencySpending;
  }

  /**
   * Get spending breakdown by billing frequency
   */
  static getSpendingByFrequency(subscriptions: Subscription[]): Record<string, number> {
    const frequencySpending: Record<string, number> = {};
    
    subscriptions
      .filter(sub => sub.status === 'active')
      .forEach(sub => {
        const monthlyAmount = this.getMonthlyAmount(sub);
        frequencySpending[sub.billing_frequency] = (frequencySpending[sub.billing_frequency] || 0) + monthlyAmount;
      });
    
    return frequencySpending;
  }

  /**
   * Generate subscription trends over the last 12 months
   */
  static getSubscriptionTrends(subscriptions: Subscription[]): {
    month: string;
    count: number;
    totalAmount: number;
  }[] {
    const trends: Record<string, { count: number; totalAmount: number }> = {};
    
    // Initialize last 12 months
    for (let i = 11; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = date.toISOString().slice(0, 7); // YYYY-MM format
      trends[monthKey] = { count: 0, totalAmount: 0 };
    }
    
    // Count subscriptions created each month
    subscriptions.forEach(sub => {
      const createdMonth = sub.created_at.slice(0, 7);
      if (trends[createdMonth]) {
        trends[createdMonth].count++;
        trends[createdMonth].totalAmount += this.getMonthlyAmount(sub);
      }
    });
    
    return Object.entries(trends).map(([month, data]) => ({
      month,
      count: data.count,
      totalAmount: data.totalAmount
    }));
  }

  /**
   * Generate cost optimization suggestions
   */
  static getCostOptimizationSuggestions(subscriptions: Subscription[]): string[] {
    const suggestions: string[] = [];
    const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active');
    
    // Check for duplicate providers
    const providerCounts: Record<string, Subscription[]> = {};
    activeSubscriptions.forEach(sub => {
      const provider = sub.provider.toLowerCase();
      if (!providerCounts[provider]) {
        providerCounts[provider] = [];
      }
      providerCounts[provider].push(sub);
    });
    
    Object.entries(providerCounts).forEach(([provider, subs]) => {
      if (subs.length > 1) {
        suggestions.push(`You have ${subs.length} subscriptions with ${provider}. Consider consolidating to save costs.`);
      }
    });
    
    // Check for high-cost subscriptions
    const highCostThreshold = 100; // $100 per month
    const highCostSubs = activeSubscriptions.filter(sub => 
      this.getMonthlyAmount(sub) > highCostThreshold
    );
    
    if (highCostSubs.length > 0) {
      suggestions.push(`You have ${highCostSubs.length} high-cost subscription(s) (>${highCostThreshold}/month). Review if all features are being utilized.`);
    }
    
    // Check for annual vs monthly savings opportunities
    const monthlySubscriptions = activeSubscriptions.filter(sub => sub.billing_frequency === 'monthly');
    if (monthlySubscriptions.length > 0) {
      suggestions.push(`Consider switching ${monthlySubscriptions.length} monthly subscription(s) to annual billing for potential 10-20% savings.`);
    }
    
    // Check for unused subscriptions (older than 6 months)
    const oldSubscriptions = activeSubscriptions.filter(sub => {
      const createdDate = new Date(sub.created_at);
      const monthsOld = (Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24 * 30);
      return monthsOld > 6;
    });
    
    if (oldSubscriptions.length > 0) {
      suggestions.push(`Review ${oldSubscriptions.length} long-running subscription(s) to ensure they're still needed.`);
    }
    
    // Check for similar categories
    const categorySpending = this.getSpendingByCategory(subscriptions);
    Object.entries(categorySpending).forEach(([category, amount]) => {
      if (amount > 200) { // More than $200/month in one category
        const categoryCount = activeSubscriptions.filter(sub => sub.category === category).length;
        if (categoryCount > 2) {
          suggestions.push(`You're spending $${amount.toFixed(2)}/month on ${category} across ${categoryCount} subscriptions. Consider consolidating.`);
        }
      }
    });
    
    return suggestions;
  }

  /**
   * Generate comprehensive subscription analytics
   */
  static generateAnalytics(subscriptions: Subscription[]): SubscriptionAnalytics {
    const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active');
    const totalMonthlySpending = this.getTotalMonthlySpending(subscriptions);
    
    return {
      totalMonthlySpending,
      totalYearlySpending: this.getTotalYearlySpending(subscriptions),
      averageSubscriptionCost: activeSubscriptions.length > 0 ? totalMonthlySpending / activeSubscriptions.length : 0,
      mostExpensiveSubscription: activeSubscriptions.length > 0 
        ? activeSubscriptions.reduce((max, sub) => 
            this.getMonthlyAmount(sub) > this.getMonthlyAmount(max) ? sub : max
          )
        : null,
      upcomingPayments: this.getUpcomingPayments(subscriptions),
      overduePayments: this.getOverduePayments(subscriptions),
      spendingByCategory: this.getSpendingByCategory(subscriptions),
      spendingByCurrency: this.getSpendingByCurrency(subscriptions),
      spendingByFrequency: this.getSpendingByFrequency(subscriptions),
      subscriptionTrends: this.getSubscriptionTrends(subscriptions),
      costOptimizationSuggestions: this.getCostOptimizationSuggestions(subscriptions),
    };
  }

  /**
   * Calculate potential annual savings by switching to yearly billing
   */
  static calculateAnnualSavings(subscriptions: Subscription[], savingsPercentage: number = 15): number {
    const monthlySubscriptions = subscriptions.filter(
      sub => sub.status === 'active' && sub.billing_frequency === 'monthly'
    );
    
    const currentYearlyCost = monthlySubscriptions.reduce(
      (total, sub) => total + (sub.amount * 12), 0
    );
    
    return currentYearlyCost * (savingsPercentage / 100);
  }

  /**
   * Get subscription renewal calendar for the next N months
   */
  static getRenewalCalendar(subscriptions: Subscription[], months: number = 12): {
    date: string;
    subscriptions: Subscription[];
    totalAmount: number;
  }[] {
    const calendar: Record<string, Subscription[]> = {};
    const today = new Date();
    
    subscriptions
      .filter(sub => sub.status === 'active')
      .forEach(sub => {
        let currentDate = new Date(sub.next_payment_date);
        const endDate = new Date(today.getTime() + months * 30 * 24 * 60 * 60 * 1000);
        
        while (currentDate <= endDate) {
          const dateKey = currentDate.toISOString().split('T')[0];
          if (!calendar[dateKey]) {
            calendar[dateKey] = [];
          }
          calendar[dateKey].push(sub);
          
          // Calculate next renewal date
          currentDate = this.calculateNextPaymentDate({ ...sub, next_payment_date: dateKey });
        }
      });
    
    return Object.entries(calendar)
      .map(([date, subs]) => ({
        date,
        subscriptions: subs,
        totalAmount: subs.reduce((total, sub) => total + sub.amount, 0)
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }
}