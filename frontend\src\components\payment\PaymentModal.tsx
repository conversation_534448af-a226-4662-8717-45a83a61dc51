import React, { useState } from 'react';
import { Payment, usePayment } from '../../contexts/PaymentContext';
import { PaymentForm } from './PaymentForm';
import { FileUpload } from './FileUpload';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  payment?: Payment;
  onSubmit: (data: any) => Promise<void>;
  isLoading?: boolean;
  title: string;
  paymentMethods?: string[];
}

export const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  payment,
  onSubmit,
  isLoading = false,
  title,
  paymentMethods = []
}) => {
  const { uploadAttachments } = usePayment();
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [uploadingFiles, setUploadingFiles] = useState(false);
  const [createdPaymentId, setCreatedPaymentId] = useState<string | null>(null);

  if (!isOpen) return null;

  const handleSubmit = async (data: any) => {
    const result = await onSubmit(data);
    
    // If this is a new payment and we have files to upload, show file upload
    if (!payment && result && typeof result === 'object' && 'id' in result) {
      setCreatedPaymentId(result.id);
      setShowFileUpload(true);
    } else {
      onClose();
    }
  };

  const handleFilesSelected = async (files: File[]) => {
    if (!createdPaymentId) return;

    setUploadingFiles(true);
    try {
      await uploadAttachments(createdPaymentId, files);
      onClose();
    } catch (error) {
      console.error('Failed to upload files:', error);
    } finally {
      setUploadingFiles(false);
    }
  };

  const handleSkipUpload = () => {
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={!showFileUpload ? onClose : undefined}
        ></div>

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  {showFileUpload ? 'Upload Receipt Files' : title}
                </h3>
                
                {showFileUpload ? (
                  <div className="space-y-4">
                    <p className="text-sm text-gray-600">
                      Payment created successfully! You can now upload receipt files (optional).
                    </p>
                    
                    <FileUpload
                      onFilesSelected={handleFilesSelected}
                      disabled={uploadingFiles}
                      maxFiles={5}
                      acceptedTypes={['image/jpeg', 'image/png', 'image/gif', 'application/pdf']}
                    />
                    
                    <div className="flex justify-end space-x-3">
                      <button
                        type="button"
                        onClick={handleSkipUpload}
                        disabled={uploadingFiles}
                        className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Skip Upload
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          // Trigger file input click
                          const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
                          fileInput?.click();
                        }}
                        disabled={uploadingFiles}
                        className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {uploadingFiles ? 'Uploading...' : 'Select Files'}
                      </button>
                    </div>
                  </div>
                ) : (
                  <PaymentForm
                    payment={payment}
                    onSubmit={handleSubmit}
                    onCancel={onClose}
                    isLoading={isLoading}
                    paymentMethods={paymentMethods}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};