import React, { useState, useEffect } from 'react';
import { Subscription, useSubscription } from '../contexts/SubscriptionContext';
import { useBusiness } from '../contexts/BusinessContext';
import { SubscriptionTable } from '../components/subscription/SubscriptionTable';
import { SubscriptionModal } from '../components/subscription/SubscriptionModal';
import { DeleteSubscriptionModal } from '../components/subscription/DeleteSubscriptionModal';
import { BusinessSelector } from '../components/business/BusinessSelector';
import { useNavigate } from 'react-router-dom';

export const SubscriptionPage: React.FC = () => {
  const { 
    subscriptions, 
    categories, 
    loading, 
    error, 
    createSubscription, 
    updateSubscription, 
    deleteSubscription,
    fetchSubscriptions
  } = useSubscription();
  
  const { currentBusiness } = useBusiness();
  const navigate = useNavigate();
  
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedSubscription, setSelectedSubscription] = useState<Subscription | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [filters, setFilters] = useState({
    status: '',
    category: '',
    currency: ''
  });

  // Fetch subscriptions when business changes or filters change
  useEffect(() => {
    const subscriptionFilters = {
      ...(currentBusiness && { business_id: currentBusiness.id }),
      ...(filters.status && { status: filters.status as any }),
      ...(filters.category && { category: filters.category }),
      ...(filters.currency && { currency: filters.currency as any })
    };
    
    fetchSubscriptions(subscriptionFilters);
  }, [currentBusiness, filters, fetchSubscriptions]);

  const handleCreateSubscription = async (data: any) => {
    setIsLoading(true);
    try {
      await createSubscription(data);
      setIsCreateModalOpen(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditSubscription = async (data: any) => {
    if (!selectedSubscription) return;
    
    setIsLoading(true);
    try {
      await updateSubscription(selectedSubscription.id, data);
      setIsEditModalOpen(false);
      setSelectedSubscription(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteSubscription = async (subscription: Subscription) => {
    await deleteSubscription(subscription.id);
    setIsDeleteModalOpen(false);
    setSelectedSubscription(null);
  };

  const openEditModal = (subscription: Subscription) => {
    setSelectedSubscription(subscription);
    setIsEditModalOpen(true);
  };

  const openDeleteModal = (subscription: Subscription) => {
    setSelectedSubscription(subscription);
    setIsDeleteModalOpen(true);
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      status: '',
      category: '',
      currency: ''
    });
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="md:flex md:items-center md:justify-between mb-8">
        <div className="flex-1 min-w-0">
          <h1 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Subscription Management
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Track and manage your business subscriptions
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <button
            onClick={() => setIsCreateModalOpen(true)}
            disabled={!currentBusiness}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Subscription
          </button>
        </div>
      </div>

      {/* Business Selector and Filters */}
      <div className="bg-white shadow rounded-lg mb-6">
        <div className="px-4 py-5 sm:p-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business
              </label>
              <BusinessSelector />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category
              </label>
              <select
                value={filters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Categories</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Currency
              </label>
              <select
                value={filters.currency}
                onChange={(e) => handleFilterChange('currency', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Currencies</option>
                <option value="USD">USD</option>
                <option value="GBP">GBP</option>
                <option value="KWD">KWD</option>
              </select>
            </div>

            <div className="flex items-end">
              <button
                onClick={clearFilters}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* No Business Selected */}
      {!currentBusiness && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
          <p className="text-yellow-800 text-sm">
            Please select a business to view and manage subscriptions.
          </p>
        </div>
      )}

      {/* Subscriptions Table */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <SubscriptionTable
            subscriptions={subscriptions}
            onEdit={openEditModal}
            onDelete={openDeleteModal}
            onView={(subscription) => navigate(`/subscriptions/${subscription.id}`)}
            loading={loading}
          />
        </div>
      </div>

      {/* Modals */}
      <SubscriptionModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateSubscription}
        isLoading={isLoading}
        title="Create New Subscription"
        categories={categories}
      />

      <SubscriptionModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedSubscription(null);
        }}
        subscription={selectedSubscription || undefined}
        onSubmit={handleEditSubscription}
        isLoading={isLoading}
        title="Edit Subscription"
        categories={categories}
      />

      <DeleteSubscriptionModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setSelectedSubscription(null);
        }}
        subscription={selectedSubscription}
        onConfirm={handleDeleteSubscription}
      />
    </div>
  );
};