import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Subscription } from '../../contexts/SubscriptionContext';
import { useBusiness } from '../../contexts/BusinessContext';

const subscriptionSchema = z.object({
  business_id: z.string().min(1, 'Business is required'),
  service_name: z.string().min(1, 'Service name is required').max(100, 'Service name too long'),
  provider: z.string().min(1, 'Provider is required').max(100, 'Provider name too long'),
  amount: z.number().positive('Amount must be positive').max(999999.99, 'Amount too large'),
  currency: z.enum(['USD', 'GBP', 'KWD']),
  billing_frequency: z.enum(['monthly', 'quarterly', 'yearly']),
  category: z.string().max(50, 'Category name too long').optional(),
  status: z.enum(['active', 'inactive', 'cancelled']),
  next_payment_date: z.string().min(1, 'Next payment date is required')
});

type SubscriptionFormData = z.infer<typeof subscriptionSchema>;

interface SubscriptionFormProps {
  subscription?: Subscription;
  onSubmit: (data: SubscriptionFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  categories?: string[];
}

export const SubscriptionForm: React.FC<SubscriptionFormProps> = ({
  subscription,
  onSubmit,
  onCancel,
  isLoading = false,
  categories = []
}) => {
  const { businesses } = useBusiness();
  const [error, setError] = useState<string | null>(null);
  const [customCategory, setCustomCategory] = useState('');
  const [showCustomCategory, setShowCustomCategory] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<SubscriptionFormData>({
    resolver: zodResolver(subscriptionSchema),
    defaultValues: {
      business_id: subscription?.business_id || '',
      service_name: subscription?.service_name || '',
      provider: subscription?.provider || '',
      amount: subscription?.amount || 0,
      currency: subscription?.currency || 'USD',
      billing_frequency: subscription?.billing_frequency || 'monthly',
      category: subscription?.category || '',
      status: subscription?.status || 'active',
      next_payment_date: subscription?.next_payment_date 
        ? new Date(subscription.next_payment_date).toISOString().split('T')[0]
        : ''
    }
  });

  const selectedCategory = watch('category');

  useEffect(() => {
    if (selectedCategory === 'custom') {
      setShowCustomCategory(true);
    } else {
      setShowCustomCategory(false);
      setCustomCategory('');
    }
  }, [selectedCategory]);

  const handleFormSubmit = async (data: SubscriptionFormData) => {
    setError(null);
    try {
      // If custom category is selected, use the custom category value
      if (data.category === 'custom' && customCategory.trim()) {
        data.category = customCategory.trim();
      } else if (data.category === 'custom') {
        data.category = '';
      }

      // Convert date to ISO string
      const nextPaymentDate = new Date(data.next_payment_date);
      nextPaymentDate.setHours(12, 0, 0, 0); // Set to noon to avoid timezone issues
      
      await onSubmit({
        ...data,
        next_payment_date: nextPaymentDate.toISOString()
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const predefinedCategories = [
    'Software & Tools',
    'Entertainment',
    'Utilities',
    'Marketing',
    'Communication',
    'Storage & Backup',
    'Security',
    'Analytics',
    'Design',
    'Development',
    'Other'
  ];

  const allCategories = [...new Set([...predefinedCategories, ...categories])];

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="business_id" className="block text-sm font-medium text-gray-700">
            Business *
          </label>
          <select
            {...register('business_id')}
            id="business_id"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select a business</option>
            {businesses.map((business) => (
              <option key={business.id} value={business.id}>
                {business.name}
              </option>
            ))}
          </select>
          {errors.business_id && (
            <p className="mt-1 text-sm text-red-600">{errors.business_id.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="status" className="block text-sm font-medium text-gray-700">
            Status *
          </label>
          <select
            {...register('status')}
            id="status"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="cancelled">Cancelled</option>
          </select>
          {errors.status && (
            <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="service_name" className="block text-sm font-medium text-gray-700">
            Service Name *
          </label>
          <input
            {...register('service_name')}
            type="text"
            id="service_name"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="e.g., Netflix, Office 365"
          />
          {errors.service_name && (
            <p className="mt-1 text-sm text-red-600">{errors.service_name.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="provider" className="block text-sm font-medium text-gray-700">
            Provider *
          </label>
          <input
            {...register('provider')}
            type="text"
            id="provider"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="e.g., Netflix Inc., Microsoft"
          />
          {errors.provider && (
            <p className="mt-1 text-sm text-red-600">{errors.provider.message}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
            Amount *
          </label>
          <input
            {...register('amount', { valueAsNumber: true })}
            type="number"
            step="0.01"
            min="0"
            id="amount"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="0.00"
          />
          {errors.amount && (
            <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="currency" className="block text-sm font-medium text-gray-700">
            Currency *
          </label>
          <select
            {...register('currency')}
            id="currency"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="USD">USD</option>
            <option value="GBP">GBP</option>
            <option value="KWD">KWD</option>
          </select>
          {errors.currency && (
            <p className="mt-1 text-sm text-red-600">{errors.currency.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="billing_frequency" className="block text-sm font-medium text-gray-700">
            Billing Frequency *
          </label>
          <select
            {...register('billing_frequency')}
            id="billing_frequency"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="monthly">Monthly</option>
            <option value="quarterly">Quarterly</option>
            <option value="yearly">Yearly</option>
          </select>
          {errors.billing_frequency && (
            <p className="mt-1 text-sm text-red-600">{errors.billing_frequency.message}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="category" className="block text-sm font-medium text-gray-700">
            Category
          </label>
          <select
            {...register('category')}
            id="category"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select a category</option>
            {allCategories.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
            <option value="custom">Custom Category...</option>
          </select>
          {errors.category && (
            <p className="mt-1 text-sm text-red-600">{errors.category.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="next_payment_date" className="block text-sm font-medium text-gray-700">
            Next Payment Date *
          </label>
          <input
            {...register('next_payment_date')}
            type="date"
            id="next_payment_date"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
          {errors.next_payment_date && (
            <p className="mt-1 text-sm text-red-600">{errors.next_payment_date.message}</p>
          )}
        </div>
      </div>

      {showCustomCategory && (
        <div>
          <label htmlFor="custom_category" className="block text-sm font-medium text-gray-700">
            Custom Category
          </label>
          <input
            type="text"
            id="custom_category"
            value={customCategory}
            onChange={(e) => setCustomCategory(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter custom category name"
          />
        </div>
      )}

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isLoading}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Saving...' : subscription ? 'Update Subscription' : 'Create Subscription'}
        </button>
      </div>
    </form>
  );
};