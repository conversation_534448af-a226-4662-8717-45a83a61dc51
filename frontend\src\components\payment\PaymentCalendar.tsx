import React, { useState, useEffect } from 'react';
import { usePayment, Payment } from '../../contexts/PaymentContext';
import { useSubscription } from '../../contexts/SubscriptionContext';

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  payments: Payment[];
  upcomingPayments: any[];
}

interface PaymentCalendarProps {
  onPaymentClick?: (payment: Payment) => void;
  onDateClick?: (date: Date) => void;
}

export const PaymentCalendar: React.FC<PaymentCalendarProps> = ({
  onPaymentClick,
  onDateClick
}) => {
  const { payments, loading } = usePayment();
  const { upcomingPayments } = useSubscription();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [calendarDays, setCalendarDays] = useState<CalendarDay[]>([]);

  useEffect(() => {
    generateCalendarDays();
  }, [currentDate, payments, upcomingPayments]);

  const generateCalendarDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    // Get first day of the month and last day
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    
    // Get first day of the calendar (might be from previous month)
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    // Get last day of the calendar (might be from next month)
    const endDate = new Date(lastDay);
    endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()));
    
    const days: CalendarDay[] = [];
    const currentDateIter = new Date(startDate);
    
    while (currentDateIter <= endDate) {
      const dateStr = currentDateIter.toISOString().split('T')[0];
      
      // Filter payments for this date
      const dayPayments = payments.filter(payment => 
        payment.payment_date.split('T')[0] === dateStr
      );
      
      // Filter upcoming payments for this date
      const dayUpcomingPayments = upcomingPayments.filter(subscription => 
        subscription.next_payment_date.split('T')[0] === dateStr
      );
      
      days.push({
        date: new Date(currentDateIter),
        isCurrentMonth: currentDateIter.getMonth() === month,
        payments: dayPayments,
        upcomingPayments: dayUpcomingPayments
      });
      
      currentDateIter.setDate(currentDateIter.getDate() + 1);
    }
    
    setCalendarDays(days);
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  const formatMonth = (date: Date) => {
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'refunded': return 'bg-gray-100 text-gray-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Calendar Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-4">
          <h2 className="text-lg font-semibold text-gray-900">
            {formatMonth(currentDate)}
          </h2>
          <button
            onClick={goToToday}
            className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
          >
            Today
          </button>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => navigateMonth('prev')}
            className="p-2 hover:bg-gray-100 rounded-md"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button
            onClick={() => navigateMonth('next')}
            className="p-2 hover:bg-gray-100 rounded-md"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="p-4">
        {/* Day Headers */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar Days */}
        <div className="grid grid-cols-7 gap-1">
          {calendarDays.map((day, index) => (
            <div
              key={index}
              className={`
                min-h-[100px] p-2 border border-gray-200 cursor-pointer hover:bg-gray-50
                ${!day.isCurrentMonth ? 'bg-gray-50 text-gray-400' : ''}
                ${isToday(day.date) ? 'bg-blue-50 border-blue-200' : ''}
              `}
              onClick={() => onDateClick?.(day.date)}
            >
              <div className="flex justify-between items-start mb-1">
                <span className={`text-sm font-medium ${isToday(day.date) ? 'text-blue-600' : ''}`}>
                  {day.date.getDate()}
                </span>
              </div>

              {/* Payments */}
              <div className="space-y-1">
                {day.payments.slice(0, 2).map(payment => (
                  <div
                    key={payment.id}
                    className={`
                      text-xs px-2 py-1 rounded cursor-pointer
                      ${getPaymentStatusColor(payment.status)}
                    `}
                    onClick={(e) => {
                      e.stopPropagation();
                      onPaymentClick?.(payment);
                    }}
                    title={`${payment.currency} ${payment.amount} - ${payment.status}`}
                  >
                    {payment.currency} {payment.amount}
                  </div>
                ))}

                {/* Upcoming Payments */}
                {day.upcomingPayments.slice(0, 2).map(subscription => (
                  <div
                    key={subscription.id}
                    className="text-xs px-2 py-1 rounded bg-orange-100 text-orange-800 border border-orange-200"
                    title={`Due: ${subscription.service_name} - ${subscription.currency} ${subscription.amount}`}
                  >
                    Due: {subscription.currency} {subscription.amount}
                  </div>
                ))}

                {/* Show more indicator */}
                {(day.payments.length + day.upcomingPayments.length) > 2 && (
                  <div className="text-xs text-gray-500 text-center">
                    +{(day.payments.length + day.upcomingPayments.length) - 2} more
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Legend */}
      <div className="px-4 py-3 border-t border-gray-200 bg-gray-50">
        <div className="flex flex-wrap gap-4 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-100 border border-green-200 rounded"></div>
            <span className="text-gray-600">Completed</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-yellow-100 border border-yellow-200 rounded"></div>
            <span className="text-gray-600">Pending</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-100 border border-red-200 rounded"></div>
            <span className="text-gray-600">Failed</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-orange-100 border border-orange-200 rounded"></div>
            <span className="text-gray-600">Due</span>
          </div>
        </div>
      </div>
    </div>
  );
};
