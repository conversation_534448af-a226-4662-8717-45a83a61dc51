import axios from 'axios';
import { supabaseAdmin } from '../config/supabase';
import { createError } from '../middleware/errorHandler';
import { config } from '../config/config';

// Simple in-memory cache
interface CacheEntry {
  data: any;
  timestamp: number;
  ttl: number;
}

class SimpleCache {
  private cache = new Map<string, CacheEntry>();

  set(key: string, data: any, ttl: number = 24 * 60 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  get(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  clear(): void {
    this.cache.clear();
  }

  delete(key: string): void {
    this.cache.delete(key);
  }
}

export interface ExchangeRate {
  id: string;
  date: string;
  from_currency: string;
  to_currency: string;
  rate: number;
  created_at: string;
  updated_at: string;
}

export interface CurrencyConversion {
  amount: number;
  fromCurrency: string;
  toCurrency: string;
  convertedAmount: number;
  rate: number;
  date: string;
}

export class ExchangeRateService {
  private static readonly SUPPORTED_CURRENCIES = ['USD', 'GBP', 'KWD'];
  private static readonly API_BASE_URL = 'https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1';
  private static readonly CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
  private static readonly MAX_RETRIES = 3;
  private static readonly RETRY_DELAY = 1000; // 1 second
  private static cache = new SimpleCache();

  /**
   * Fetch current exchange rates from external API with retry logic
   */
  static async fetchCurrentRates(): Promise<{ [key: string]: number }> {
    const cacheKey = 'current_rates';
    const cachedRates = this.cache.get(cacheKey);
    
    if (cachedRates) {
      console.log('Using cached exchange rates');
      return cachedRates;
    }

    let lastError: any;
    
    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        console.log(`Fetching exchange rates (attempt ${attempt}/${this.MAX_RETRIES})`);
        
        const rates: { [key: string]: number } = {};

        // Fetch USD to KWD with timeout and retry
        try {
          const usdResponse = await axios.get(
            `${this.API_BASE_URL}/currencies/usd.json`,
            { 
              timeout: 10000,
              headers: {
                'User-Agent': 'Subscription-Tracker/1.0'
              }
            }
          );
          
          if (usdResponse.data?.usd?.kwd) {
            rates['USD_KWD'] = Number(usdResponse.data.usd.kwd);
          }
        } catch (error) {
          console.warn('Failed to fetch USD rates:', error);
        }

        // Fetch GBP to KWD with timeout and retry
        try {
          const gbpResponse = await axios.get(
            `${this.API_BASE_URL}/currencies/gbp.json`,
            { 
              timeout: 10000,
              headers: {
                'User-Agent': 'Subscription-Tracker/1.0'
              }
            }
          );
          
          if (gbpResponse.data?.gbp?.kwd) {
            rates['GBP_KWD'] = Number(gbpResponse.data.gbp.kwd);
          }
        } catch (error) {
          console.warn('Failed to fetch GBP rates:', error);
        }

        // If we got at least one rate, consider it successful
        if (Object.keys(rates).length > 0) {
          // Cache the successful result
          this.cache.set(cacheKey, rates, this.CACHE_TTL);
          console.log(`Successfully fetched ${Object.keys(rates).length} exchange rates`);
          return rates;
        }

        throw new Error('No exchange rates received from API');
      } catch (error) {
        lastError = error;
        console.error(`Attempt ${attempt} failed:`, error);
        
        if (attempt < this.MAX_RETRIES) {
          const delay = this.RETRY_DELAY * Math.pow(2, attempt - 1); // Exponential backoff
          console.log(`Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // If all retries failed, try to get fallback rates from database
    console.log('All API attempts failed, trying fallback from database...');
    try {
      const fallbackRates = await this.getFallbackRates();
      if (Object.keys(fallbackRates).length > 0) {
        console.log('Using fallback rates from database');
        return fallbackRates;
      }
    } catch (fallbackError) {
      console.error('Fallback rates also failed:', fallbackError);
    }

    throw createError(`Failed to fetch current exchange rates after ${this.MAX_RETRIES} attempts: ${lastError?.message}`, 500);
  }

  /**
   * Get fallback rates from the most recent database entries
   */
  private static async getFallbackRates(): Promise<{ [key: string]: number }> {
    try {
      const { data, error } = await supabaseAdmin
        .from('exchange_rates')
        .select('from_currency, to_currency, rate')
        .in('from_currency', ['USD', 'GBP'])
        .eq('to_currency', 'KWD')
        .order('date', { ascending: false })
        .limit(2);

      if (error) throw error;

      const rates: { [key: string]: number } = {};
      data?.forEach(rate => {
        rates[`${rate.from_currency}_${rate.to_currency}`] = rate.rate;
      });

      return rates;
    } catch (error) {
      console.error('Failed to get fallback rates:', error);
      return {};
    }
  }

  /**
   * Store exchange rates in database
   */
  static async storeRates(rates: { [key: string]: number }, date: string = new Date().toISOString().split('T')[0]) {
    try {
      const ratesToInsert = [];

      for (const [currencyPair, rate] of Object.entries(rates)) {
        const [fromCurrency, toCurrency] = currencyPair.split('_');
        ratesToInsert.push({
          date,
          from_currency: fromCurrency,
          to_currency: toCurrency,
          rate: Number(rate.toFixed(6))
        });
      }

      const { error } = await supabaseAdmin
        .from('exchange_rates')
        .upsert(ratesToInsert, {
          onConflict: 'date,from_currency,to_currency'
        });

      if (error) {
        throw createError(`Failed to store exchange rates: ${error.message}`, 500);
      }

      return ratesToInsert;
    } catch (error) {
      console.error('Failed to store exchange rates:', error);
      throw error;
    }
  }

  /**
   * Get exchange rate for a specific date and currency pair
   */
  static async getRate(fromCurrency: string, toCurrency: string, date: string = new Date().toISOString().split('T')[0]): Promise<number> {
    try {
      // If same currency, return 1
      if (fromCurrency === toCurrency) {
        return 1;
      }

      // Check cache first for current date
      const isToday = date === new Date().toISOString().split('T')[0];
      const cacheKey = `rate_${fromCurrency}_${toCurrency}_${date}`;
      
      if (isToday) {
        const cachedRate = this.cache.get(cacheKey);
        if (cachedRate !== null) {
          return cachedRate;
        }
      }

      // Check database first
      const { data, error } = await supabaseAdmin
        .from('exchange_rates')
        .select('rate')
        .eq('from_currency', fromCurrency)
        .eq('to_currency', toCurrency)
        .eq('date', date)
        .single();

      if (!error && data) {
        // Cache the result if it's for today
        if (isToday) {
          this.cache.set(cacheKey, data.rate, this.CACHE_TTL);
        }
        return data.rate;
      }

      // If not found for exact date, try to get the most recent rate
      const { data: recentData, error: recentError } = await supabaseAdmin
        .from('exchange_rates')
        .select('rate, date')
        .eq('from_currency', fromCurrency)
        .eq('to_currency', toCurrency)
        .order('date', { ascending: false })
        .limit(1)
        .single();

      if (!recentError && recentData) {
        // Cache the result if it's for today
        if (isToday) {
          this.cache.set(cacheKey, recentData.rate, this.CACHE_TTL);
        }
        return recentData.rate;
      }

      // If no rate found in database, fetch from API and store
      const currentRates = await this.fetchCurrentRates();
      const rateKey = `${fromCurrency}_${toCurrency}`;
      
      if (currentRates[rateKey]) {
        await this.storeRates(currentRates);
        // Cache the result
        if (isToday) {
          this.cache.set(cacheKey, currentRates[rateKey], this.CACHE_TTL);
        }
        return currentRates[rateKey];
      }

      throw createError(`Exchange rate not available for ${fromCurrency} to ${toCurrency}`, 404);
    } catch (error) {
      console.error('Failed to get exchange rate:', error);
      throw error;
    }
  }

  /**
   * Convert amount from one currency to another
   */
  static async convertCurrency(
    amount: number,
    fromCurrency: string,
    toCurrency: string,
    date?: string
  ): Promise<CurrencyConversion> {
    try {
      if (!this.SUPPORTED_CURRENCIES.includes(fromCurrency) || !this.SUPPORTED_CURRENCIES.includes(toCurrency)) {
        throw createError('Unsupported currency', 400);
      }

      const conversionDate = date || new Date().toISOString().split('T')[0];
      const rate = await this.getRate(fromCurrency, toCurrency, conversionDate);
      const convertedAmount = Number((amount * rate).toFixed(3));

      return {
        amount,
        fromCurrency,
        toCurrency,
        convertedAmount,
        rate,
        date: conversionDate
      };
    } catch (error) {
      console.error('Failed to convert currency:', error);
      throw error;
    }
  }

  /**
   * Get historical rates for a date range
   */
  static async getHistoricalRates(
    fromCurrency: string,
    toCurrency: string,
    startDate: string,
    endDate: string
  ): Promise<ExchangeRate[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('exchange_rates')
        .select('*')
        .eq('from_currency', fromCurrency)
        .eq('to_currency', toCurrency)
        .gte('date', startDate)
        .lte('date', endDate)
        .order('date', { ascending: true });

      if (error) {
        throw createError(`Failed to fetch historical rates: ${error.message}`, 500);
      }

      return data || [];
    } catch (error) {
      console.error('Failed to get historical rates:', error);
      throw error;
    }
  }

  /**
   * Update rates manually (admin function)
   */
  static async updateRateManually(
    fromCurrency: string,
    toCurrency: string,
    rate: number,
    date: string = new Date().toISOString().split('T')[0]
  ): Promise<ExchangeRate> {
    try {
      const { data, error } = await supabaseAdmin
        .from('exchange_rates')
        .upsert({
          date,
          from_currency: fromCurrency,
          to_currency: toCurrency,
          rate: Number(rate.toFixed(6))
        }, {
          onConflict: 'date,from_currency,to_currency'
        })
        .select()
        .single();

      if (error) {
        throw createError(`Failed to update exchange rate: ${error.message}`, 500);
      }

      return data;
    } catch (error) {
      console.error('Failed to update rate manually:', error);
      throw error;
    }
  }

  /**
   * Get all supported currencies
   */
  static getSupportedCurrencies(): string[] {
    return [...this.SUPPORTED_CURRENCIES];
  }

  /**
   * Validate currency code
   */
  static isValidCurrency(currency: string): boolean {
    return this.SUPPORTED_CURRENCIES.includes(currency.toUpperCase());
  }

  /**
   * Clear cache
   */
  static clearCache(): void {
    this.cache.clear();
    console.log('Exchange rate cache cleared');
  }

  /**
   * Get cache statistics
   */
  static getCacheStats(): { size: number; keys: string[] } {
    const keys: string[] = [];
    this.cache['cache'].forEach((_, key) => keys.push(key));
    return {
      size: this.cache['cache'].size,
      keys
    };
  }

  /**
   * Invalidate specific cache entry
   */
  static invalidateCache(key: string): void {
    this.cache.delete(key);
  }
}