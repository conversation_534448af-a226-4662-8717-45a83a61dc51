import { Business } from '../contexts/BusinessContext';
import { Subscription } from '../contexts/SubscriptionContext';

export interface BusinessAnalytics {
  totalMonthlySpending: number;
  spendingByCategory: Record<string, number>;
  spendingByCurrency: Record<string, number>;
  subscriptionTrends: {
    month: string;
    count: number;
    amount: number;
  }[];
  upcomingRenewals: Subscription[];
  costOptimizationSuggestions: string[];
}

export class BusinessAnalyticsService {
  static calculateMonthlySpending(subscriptions: Subscription[]): number {
    return subscriptions
      .filter(sub => sub.status === 'active')
      .reduce((total, sub) => {
        let monthlyAmount = sub.amount;
        
        switch (sub.billing_frequency) {
          case 'quarterly':
            monthlyAmount = sub.amount / 3;
            break;
          case 'yearly':
            monthlyAmount = sub.amount / 12;
            break;
          default:
            monthlyAmount = sub.amount;
        }
        
        return total + monthlyAmount;
      }, 0);
  }

  static getSpendingByCategory(subscriptions: Subscription[]): Record<string, number> {
    const categorySpending: Record<string, number> = {};
    
    subscriptions
      .filter(sub => sub.status === 'active')
      .forEach(sub => {
        const category = sub.category || 'Other';
        let monthlyAmount = sub.amount;
        
        switch (sub.billing_frequency) {
          case 'quarterly':
            monthlyAmount = sub.amount / 3;
            break;
          case 'yearly':
            monthlyAmount = sub.amount / 12;
            break;
        }
        
        categorySpending[category] = (categorySpending[category] || 0) + monthlyAmount;
      });
    
    return categorySpending;
  }

  static getSpendingByCurrency(subscriptions: Subscription[]): Record<string, number> {
    const currencySpending: Record<string, number> = {};
    
    subscriptions
      .filter(sub => sub.status === 'active')
      .forEach(sub => {
        currencySpending[sub.currency] = (currencySpending[sub.currency] || 0) + sub.amount;
      });
    
    return currencySpending;
  }

  static getUpcomingRenewals(subscriptions: Subscription[], days: number = 30): Subscription[] {
    const today = new Date();
    const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);
    
    return subscriptions
      .filter(sub => {
        if (sub.status !== 'active') return false;
        
        const renewalDate = new Date(sub.next_payment_date);
        return renewalDate >= today && renewalDate <= futureDate;
      })
      .sort((a, b) => new Date(a.next_payment_date).getTime() - new Date(b.next_payment_date).getTime());
  }

  static generateCostOptimizationSuggestions(subscriptions: Subscription[]): string[] {
    const suggestions: string[] = [];
    const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active');
    
    // Check for duplicate services
    const serviceProviders: Record<string, Subscription[]> = {};
    activeSubscriptions.forEach(sub => {
      const key = sub.provider.toLowerCase();
      if (!serviceProviders[key]) {
        serviceProviders[key] = [];
      }
      serviceProviders[key].push(sub);
    });
    
    Object.entries(serviceProviders).forEach(([provider, subs]) => {
      if (subs.length > 1) {
        suggestions.push(`You have ${subs.length} subscriptions with ${provider}. Consider consolidating to save costs.`);
      }
    });
    
    // Check for high-cost subscriptions
    const highCostThreshold = 100; // $100 per month
    const highCostSubs = activeSubscriptions.filter(sub => {
      let monthlyAmount = sub.amount;
      if (sub.billing_frequency === 'quarterly') monthlyAmount = sub.amount / 3;
      if (sub.billing_frequency === 'yearly') monthlyAmount = sub.amount / 12;
      return monthlyAmount > highCostThreshold;
    });
    
    if (highCostSubs.length > 0) {
      suggestions.push(`You have ${highCostSubs.length} high-cost subscription(s). Review if all features are being utilized.`);
    }
    
    // Check for annual vs monthly savings opportunities
    const monthlySubscriptions = activeSubscriptions.filter(sub => sub.billing_frequency === 'monthly');
    if (monthlySubscriptions.length > 0) {
      suggestions.push(`Consider switching ${monthlySubscriptions.length} monthly subscription(s) to annual billing for potential savings.`);
    }
    
    // Check for unused subscriptions (this would require usage data)
    const oldSubscriptions = activeSubscriptions.filter(sub => {
      const createdDate = new Date(sub.created_at);
      const monthsOld = (Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24 * 30);
      return monthsOld > 6; // Older than 6 months
    });
    
    if (oldSubscriptions.length > 0) {
      suggestions.push(`Review ${oldSubscriptions.length} long-running subscription(s) to ensure they're still needed.`);
    }
    
    return suggestions;
  }

  static generateSubscriptionTrends(subscriptions: Subscription[]): {
    month: string;
    count: number;
    amount: number;
  }[] {
    const trends: Record<string, { count: number; amount: number }> = {};
    
    // Get last 12 months
    for (let i = 11; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = date.toISOString().slice(0, 7); // YYYY-MM format
      trends[monthKey] = { count: 0, amount: 0 };
    }
    
    // Count subscriptions created each month
    subscriptions.forEach(sub => {
      const createdMonth = sub.created_at.slice(0, 7);
      if (trends[createdMonth]) {
        trends[createdMonth].count++;
        trends[createdMonth].amount += sub.amount;
      }
    });
    
    return Object.entries(trends).map(([month, data]) => ({
      month,
      count: data.count,
      amount: data.amount
    }));
  }

  static generateBusinessAnalytics(business: Business, subscriptions: Subscription[]): BusinessAnalytics {
    return {
      totalMonthlySpending: this.calculateMonthlySpending(subscriptions),
      spendingByCategory: this.getSpendingByCategory(subscriptions),
      spendingByCurrency: this.getSpendingByCurrency(subscriptions),
      subscriptionTrends: this.generateSubscriptionTrends(subscriptions),
      upcomingRenewals: this.getUpcomingRenewals(subscriptions),
      costOptimizationSuggestions: this.generateCostOptimizationSuggestions(subscriptions)
    };
  }
}