import { Router } from 'express';
import { z } from 'zod';
import { asyncHandler } from '../middleware/errorHandler';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import { BusinessService } from '../services/businessService';

const router = Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Validation schemas
const businessSchema = z.object({
  name: z.string().min(1, 'Business name is required').max(100, 'Business name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  address: z.string().max(200, 'Address too long').optional(),
  contact_email: z.string().email('Invalid email format').optional(),
  contact_phone: z.string().max(20, 'Phone number too long').optional(),
  reimbursement_timeline: z.number().min(1, 'Timeline must be at least 1 day').max(365, 'Timeline cannot exceed 365 days').default(30)
});

const updateBusinessSchema = businessSchema.partial();

const searchSchema = z.object({
  q: z.string().min(1, 'Search term is required')
});

// GET /api/businesses
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const { q } = req.query;

  let businesses;
  if (q) {
    const { q: searchTerm } = searchSchema.parse({ q });
    businesses = await BusinessService.searchBusinesses(userId, searchTerm);
  } else {
    businesses = await BusinessService.getUserBusinesses(userId);
  }

  res.json({
    success: true,
    message: 'Businesses retrieved successfully',
    data: businesses
  });
}));

// GET /api/businesses/:id
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const userId = req.user!.id;
  
  const business = await BusinessService.getBusinessById(id, userId);
  
  res.json({
    success: true,
    message: 'Business retrieved successfully',
    data: business
  });
}));

// GET /api/businesses/:id/stats
router.get('/:id/stats', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const userId = req.user!.id;
  
  const stats = await BusinessService.getBusinessStats(id, userId);
  
  res.json({
    success: true,
    message: 'Business statistics retrieved successfully',
    data: stats
  });
}));

// POST /api/businesses
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const businessData = businessSchema.parse(req.body);
  
  const business = await BusinessService.createBusiness(userId, {
    name: businessData.name,
    description: businessData.description,
    address: businessData.address,
    contact_email: businessData.contact_email,
    contact_phone: businessData.contact_phone,
    reimbursement_timeline: businessData.reimbursement_timeline
  });
  
  res.status(201).json({
    success: true,
    message: 'Business created successfully',
    data: business
  });
}));

// PUT /api/businesses/:id
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const updates = updateBusinessSchema.parse(req.body);
  
  const business = await BusinessService.updateBusiness(id, userId, {
    name: updates.name,
    description: updates.description,
    address: updates.address,
    contact_email: updates.contact_email,
    contact_phone: updates.contact_phone,
    reimbursement_timeline: updates.reimbursement_timeline
  });
  
  res.json({
    success: true,
    message: 'Business updated successfully',
    data: business
  });
}));

// DELETE /api/businesses/:id
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const userId = req.user!.id;
  
  await BusinessService.deleteBusiness(id, userId);
  
  res.json({
    success: true,
    message: 'Business deleted successfully'
  });
}));

export { router as businessRoutes };