-- Enable Row Level Security on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.businesses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reimbursements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.exchange_rates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reimbursement_payments ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

-- Businesses table policies
CREATE POLICY "Users can view own businesses" ON public.businesses
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own businesses" ON public.businesses
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own businesses" ON public.businesses
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own businesses" ON public.businesses
  FOR DELETE USING (auth.uid() = user_id);

-- Subscriptions table policies
CREATE POLICY "Users can view subscriptions for their businesses" ON public.subscriptions
  FOR SELECT USING (
    business_id IN (
      SELECT id FROM public.businesses WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert subscriptions for their businesses" ON public.subscriptions
  FOR INSERT WITH CHECK (
    business_id IN (
      SELECT id FROM public.businesses WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update subscriptions for their businesses" ON public.subscriptions
  FOR UPDATE USING (
    business_id IN (
      SELECT id FROM public.businesses WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete subscriptions for their businesses" ON public.subscriptions
  FOR DELETE USING (
    business_id IN (
      SELECT id FROM public.businesses WHERE user_id = auth.uid()
    )
  );

-- Payments table policies
CREATE POLICY "Users can view payments for their subscriptions" ON public.payments
  FOR SELECT USING (
    subscription_id IN (
      SELECT s.id FROM public.subscriptions s
      JOIN public.businesses b ON s.business_id = b.id
      WHERE b.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert payments for their subscriptions" ON public.payments
  FOR INSERT WITH CHECK (
    subscription_id IN (
      SELECT s.id FROM public.subscriptions s
      JOIN public.businesses b ON s.business_id = b.id
      WHERE b.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update payments for their subscriptions" ON public.payments
  FOR UPDATE USING (
    subscription_id IN (
      SELECT s.id FROM public.subscriptions s
      JOIN public.businesses b ON s.business_id = b.id
      WHERE b.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete payments for their subscriptions" ON public.payments
  FOR DELETE USING (
    subscription_id IN (
      SELECT s.id FROM public.subscriptions s
      JOIN public.businesses b ON s.business_id = b.id
      WHERE b.user_id = auth.uid()
    )
  );

-- Reimbursements table policies
CREATE POLICY "Users can view own reimbursements" ON public.reimbursements
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own reimbursements" ON public.reimbursements
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own reimbursements" ON public.reimbursements
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own reimbursements" ON public.reimbursements
  FOR DELETE USING (auth.uid() = user_id);

-- Exchange rates table policies (read-only for all authenticated users)
CREATE POLICY "Authenticated users can view exchange rates" ON public.exchange_rates
  FOR SELECT USING (auth.role() = 'authenticated');

-- Payment attachments table policies
CREATE POLICY "Users can view attachments for their payments" ON public.payment_attachments
  FOR SELECT USING (
    payment_id IN (
      SELECT p.id FROM public.payments p
      JOIN public.subscriptions s ON p.subscription_id = s.id
      JOIN public.businesses b ON s.business_id = b.id
      WHERE b.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert attachments for their payments" ON public.payment_attachments
  FOR INSERT WITH CHECK (
    payment_id IN (
      SELECT p.id FROM public.payments p
      JOIN public.subscriptions s ON p.subscription_id = s.id
      JOIN public.businesses b ON s.business_id = b.id
      WHERE b.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete attachments for their payments" ON public.payment_attachments
  FOR DELETE USING (
    payment_id IN (
      SELECT p.id FROM public.payments p
      JOIN public.subscriptions s ON p.subscription_id = s.id
      JOIN public.businesses b ON s.business_id = b.id
      WHERE b.user_id = auth.uid()
    )
  );

-- Reimbursement payments table policies
CREATE POLICY "Users can view reimbursement payments for their data" ON public.reimbursement_payments
  FOR SELECT USING (
    reimbursement_id IN (
      SELECT id FROM public.reimbursements WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert reimbursement payments for their data" ON public.reimbursement_payments
  FOR INSERT WITH CHECK (
    reimbursement_id IN (
      SELECT id FROM public.reimbursements WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete reimbursement payments for their data" ON public.reimbursement_payments
  FOR DELETE USING (
    reimbursement_id IN (
      SELECT id FROM public.reimbursements WHERE user_id = auth.uid()
    )
  );