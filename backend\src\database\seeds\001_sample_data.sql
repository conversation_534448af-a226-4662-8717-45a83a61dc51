-- Sample exchange rates for development
INSERT INTO public.exchange_rates (date, from_currency, to_currency, rate) VALUES
  ('2024-01-01', 'USD', 'KWD', 0.307),
  ('2024-01-01', 'GBP', 'KWD', 0.389),
  ('2024-01-02', 'USD', 'KWD', 0.308),
  ('2024-01-02', 'GBP', 'KWD', 0.390),
  ('2024-01-03', 'USD', 'KWD', 0.307),
  ('2024-01-03', 'GBP', 'KWD', 0.388)
ON CONFLICT (date, from_currency, to_currency) DO NOTHING;

-- Sample subscription categories
-- Note: These will be created programmatically when users create subscriptions
-- This is just for reference of common categories

-- Sample data will be created through the API once authentication is working
-- Users will need to register and create their own businesses, subscriptions, etc.