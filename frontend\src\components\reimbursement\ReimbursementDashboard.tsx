import React, { useState, useEffect, useMemo } from 'react';
import { useReimbursement, ReimbursementStats } from '../../contexts/ReimbursementContext';
import { useBusiness } from '../../contexts/BusinessContext';

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ReactNode;
  color: string;
  trend?: {
    value: number;
    label: string;
    isPositive: boolean;
  };
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  color,
  trend
}) => (
  <div className="bg-white overflow-hidden shadow rounded-lg">
    <div className="p-5">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <div className={`h-8 w-8 ${color} rounded-md flex items-center justify-center`}>
            {icon}
          </div>
        </div>
        <div className="ml-5 w-0 flex-1">
          <dl>
            <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
            <dd className="text-lg font-semibold text-gray-900">{value}</dd>
            {subtitle && (
              <dd className="text-sm text-gray-500">{subtitle}</dd>
            )}
          </dl>
        </div>
      </div>
      {trend && (
        <div className="mt-4 flex items-center">
          <div className={`flex items-center text-sm ${trend.isPositive ? 'text-green-600' : 'text-red-600'}`}>
            {trend.isPositive ? (
              <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
              </svg>
            ) : (
              <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 7l-9.2 9.2M7 7v10h10" />
              </svg>
            )}
            {trend.value}% {trend.label}
          </div>
        </div>
      )}
    </div>
  </div>
);

interface ChartData {
  name: string;
  value: number;
  color: string;
}

const SimpleBarChart: React.FC<{ data: ChartData[]; title: string }> = ({ data, title }) => {
  const maxValue = Math.max(...data.map(d => d.value));
  
  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>
      <div className="space-y-3">
        {data.map((item, index) => (
          <div key={index} className="flex items-center">
            <div className="w-24 text-sm font-medium text-gray-900 truncate">
              {item.name}
            </div>
            <div className="flex-1 mx-4">
              <div className="bg-gray-200 rounded-full h-3">
                <div
                  className={`h-3 rounded-full ${item.color}`}
                  style={{
                    width: `${maxValue > 0 ? (item.value / maxValue) * 100 : 0}%`
                  }}
                />
              </div>
            </div>
            <div className="w-16 text-sm font-medium text-gray-900 text-right">
              {item.value}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const SimplePieChart: React.FC<{ data: ChartData[]; title: string }> = ({ data, title }) => {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  
  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>
      <div className="flex items-center justify-center mb-4">
        <div className="relative w-32 h-32">
          <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 100 100">
            {data.map((item, index) => {
              const percentage = total > 0 ? (item.value / total) * 100 : 0;
              const offset = data.slice(0, index).reduce((sum, d) => sum + (d.value / total) * 100, 0);
              const circumference = 2 * Math.PI * 40;
              const strokeDasharray = `${(percentage / 100) * circumference} ${circumference}`;
              const strokeDashoffset = -((offset / 100) * circumference);
              
              return (
                <circle
                  key={index}
                  cx="50"
                  cy="50"
                  r="40"
                  fill="none"
                  stroke={item.color.includes('blue') ? '#3B82F6' : 
                          item.color.includes('green') ? '#10B981' : 
                          item.color.includes('yellow') ? '#F59E0B' : '#EF4444'}
                  strokeWidth="8"
                  strokeDasharray={strokeDasharray}
                  strokeDashoffset={strokeDashoffset}
                  strokeLinecap="round"
                />
              );
            })}
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">{total}</div>
              <div className="text-xs text-gray-500">Total</div>
            </div>
          </div>
        </div>
      </div>
      <div className="space-y-2">
        {data.map((item, index) => (
          <div key={index} className="flex items-center justify-between">
            <div className="flex items-center">
              <div
                className={`w-3 h-3 rounded-full mr-2 ${
                  item.color.includes('blue') ? 'bg-blue-500' : 
                  item.color.includes('green') ? 'bg-green-500' : 
                  item.color.includes('yellow') ? 'bg-yellow-500' : 'bg-red-500'
                }`}
              />
              <span className="text-sm text-gray-600">{item.name}</span>
            </div>
            <div className="text-sm font-medium text-gray-900">
              {item.value} ({total > 0 ? Math.round((item.value / total) * 100) : 0}%)
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export const ReimbursementDashboard: React.FC = () => {
  const { reimbursements, loading, fetchReimbursements } = useReimbursement();
  const { businesses, currentBusiness } = useBusiness();
  const [selectedBusinessId, setSelectedBusinessId] = useState<string>('');
  const [selectedPeriod, setSelectedPeriod] = useState<string>('30'); // days
  const [businessStats, setBusinessStats] = useState<Record<string, ReimbursementStats>>({});

  // Filter reimbursements based on selected business and period
  const filteredReimbursements = useMemo(() => {
    let filtered = reimbursements;

    // Filter by business
    if (selectedBusinessId) {
      filtered = filtered.filter(r => r.business_id === selectedBusinessId);
    }

    // Filter by period
    if (selectedPeriod && selectedPeriod !== 'all') {
      const days = parseInt(selectedPeriod);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);
      filtered = filtered.filter(r => new Date(r.request_date) >= cutoffDate);
    }

    return filtered;
  }, [reimbursements, selectedBusinessId, selectedPeriod]);

  // Calculate metrics
  const metrics = useMemo(() => {
    const total = filteredReimbursements.length;
    const pending = filteredReimbursements.filter(r => r.status === 'pending').length;
    const approved = filteredReimbursements.filter(r => r.status === 'approved').length;
    const received = filteredReimbursements.filter(r => r.status === 'received').length;
    
    const totalAmount = filteredReimbursements.reduce((sum, r) => sum + r.amount, 0);
    const pendingAmount = filteredReimbursements
      .filter(r => r.status === 'pending')
      .reduce((sum, r) => sum + r.amount, 0);
    const approvedAmount = filteredReimbursements
      .filter(r => r.status === 'approved')
      .reduce((sum, r) => sum + r.amount, 0);
    const receivedAmount = filteredReimbursements
      .filter(r => r.status === 'received')
      .reduce((sum, r) => sum + r.amount, 0);

    const overdue = filteredReimbursements.filter(r => 
      (r.status === 'pending' || r.status === 'approved') && 
      new Date(r.expected_date) < new Date()
    ).length;

    const avgProcessingTime = filteredReimbursements
      .filter(r => r.status === 'received' && r.actual_date)
      .reduce((sum, r) => {
        const requestDate = new Date(r.request_date);
        const receivedDate = new Date(r.actual_date!);
        const days = Math.floor((receivedDate.getTime() - requestDate.getTime()) / (1000 * 60 * 60 * 24));
        return sum + days;
      }, 0) / Math.max(1, filteredReimbursements.filter(r => r.status === 'received').length);

    return {
      total,
      pending,
      approved,
      received,
      totalAmount,
      pendingAmount,
      approvedAmount,
      receivedAmount,
      overdue,
      avgProcessingTime: Math.round(avgProcessingTime) || 0
    };
  }, [filteredReimbursements]);

  // Chart data
  const statusChartData: ChartData[] = [
    { name: 'Pending', value: metrics.pending, color: 'bg-yellow-500' },
    { name: 'Approved', value: metrics.approved, color: 'bg-blue-500' },
    { name: 'Received', value: metrics.received, color: 'bg-green-500' }
  ];

  const amountChartData: ChartData[] = [
    { name: 'Pending', value: Math.round(metrics.pendingAmount * 1000) / 1000, color: 'bg-yellow-500' },
    { name: 'Approved', value: Math.round(metrics.approvedAmount * 1000) / 1000, color: 'bg-blue-500' },
    { name: 'Received', value: Math.round(metrics.receivedAmount * 1000) / 1000, color: 'bg-green-500' }
  ];

  const businessBreakdown = useMemo(() => {
    const breakdown: Record<string, number> = {};
    filteredReimbursements.forEach(r => {
      breakdown[r.business.name] = (breakdown[r.business.name] || 0) + 1;
    });
    
    return Object.entries(breakdown)
      .map(([name, count]) => ({ name, value: count, color: 'bg-blue-500' }))
      .sort((a, b) => b.value - a.value);
  }, [filteredReimbursements]);

  const monthlyTrend = useMemo(() => {
    const monthly: Record<string, number> = {};
    filteredReimbursements.forEach(r => {
      const month = new Date(r.request_date).toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
      monthly[month] = (monthly[month] || 0) + 1;
    });
    
    return Object.entries(monthly)
      .map(([name, count]) => ({ name, value: count, color: 'bg-indigo-500' }))
      .sort((a, b) => new Date(a.name).getTime() - new Date(b.name).getTime())
      .slice(-6); // Last 6 months
  }, [filteredReimbursements]);

  useEffect(() => {
    fetchReimbursements();
  }, [fetchReimbursements]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading dashboard...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Filters */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Reimbursement Analytics</h1>
          <p className="mt-2 text-sm text-gray-700">
            Comprehensive overview of your reimbursement requests and their status.
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="business-filter" className="block text-sm font-medium text-gray-700">
              Business
            </label>
            <select
              id="business-filter"
              value={selectedBusinessId}
              onChange={(e) => setSelectedBusinessId(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Businesses</option>
              {businesses.map(business => (
                <option key={business.id} value={business.id}>
                  {business.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="period-filter" className="block text-sm font-medium text-gray-700">
              Time Period
            </label>
            <select
              id="period-filter"
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
              <option value="365">Last year</option>
              <option value="all">All time</option>
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={() => {
                setSelectedBusinessId('');
                setSelectedPeriod('30');
              }}
              className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Reset Filters
            </button>
          </div>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Requests"
          value={metrics.total}
          icon={
            <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          }
          color="bg-blue-500"
        />

        <MetricCard
          title="Pending Amount"
          value={`KWD ${metrics.pendingAmount.toFixed(3)}`}
          subtitle={`${metrics.pending} requests`}
          icon={
            <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
          color="bg-yellow-500"
        />

        <MetricCard
          title="Approved Amount"
          value={`KWD ${metrics.approvedAmount.toFixed(3)}`}
          subtitle={`${metrics.approved} requests`}
          icon={
            <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
          color="bg-blue-500"
        />

        <MetricCard
          title="Received Amount"
          value={`KWD ${metrics.receivedAmount.toFixed(3)}`}
          subtitle={`${metrics.received} requests`}
          icon={
            <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          }
          color="bg-green-500"
        />
      </div>

      {/* Secondary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <MetricCard
          title="Overdue Requests"
          value={metrics.overdue}
          subtitle="Past expected date"
          icon={
            <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          }
          color="bg-red-500"
        />

        <MetricCard
          title="Avg Processing Time"
          value={`${metrics.avgProcessingTime} days`}
          subtitle="For completed requests"
          icon={
            <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          }
          color="bg-indigo-500"
        />

        <MetricCard
          title="Success Rate"
          value={`${metrics.total > 0 ? Math.round((metrics.received / metrics.total) * 100) : 0}%`}
          subtitle="Requests completed"
          icon={
            <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          }
          color="bg-purple-500"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SimplePieChart 
          data={statusChartData} 
          title="Requests by Status" 
        />
        <SimplePieChart 
          data={amountChartData} 
          title="Amount by Status (KWD)" 
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SimpleBarChart 
          data={businessBreakdown} 
          title="Requests by Business" 
        />
        <SimpleBarChart 
          data={monthlyTrend} 
          title="Monthly Trend" 
        />
      </div>

      {/* Summary Table */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Summary by Business
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Breakdown of reimbursement activity across all businesses
          </p>
        </div>
        <div className="border-t border-gray-200">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Business
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Requests
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Amount (KWD)
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Pending
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Received
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {businesses.map(business => {
                const businessReimbursements = filteredReimbursements.filter(r => r.business_id === business.id);
                const totalRequests = businessReimbursements.length;
                const totalAmount = businessReimbursements.reduce((sum, r) => sum + r.amount, 0);
                const pending = businessReimbursements.filter(r => r.status === 'pending').length;
                const received = businessReimbursements.filter(r => r.status === 'received').length;
                
                if (totalRequests === 0) return null;
                
                return (
                  <tr key={business.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {business.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {totalRequests}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {totalAmount.toFixed(3)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {pending}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {received}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};