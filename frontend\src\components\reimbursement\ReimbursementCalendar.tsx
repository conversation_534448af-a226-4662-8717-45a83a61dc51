import React, { useState, useMemo } from 'react';
import { Reimbursement, useReimbursement } from '../../contexts/ReimbursementContext';

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  reimbursements: {
    expected: Reimbursement[];
    received: Reimbursement[];
  };
}

interface ReimbursementCalendarProps {
  reimbursements: Reimbursement[];
  onDateClick?: (date: Date, dayReimbursements: CalendarDay['reimbursements']) => void;
  onReimbursementClick?: (reimbursement: Reimbursement) => void;
  className?: string;
}

export const ReimbursementCalendar: React.FC<ReimbursementCalendarProps> = ({
  reimbursements,
  onDateClick,
  onReimbursementClick,
  className = ''
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewType, setViewType] = useState<'expected' | 'received' | 'both'>('both');

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  const generateCalendarDays = useMemo(() => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    // Get first day of the month and calculate starting date
    const firstDayOfMonth = new Date(year, month, 1);
    const lastDayOfMonth = new Date(year, month + 1, 0);
    const startingDayOfWeek = firstDayOfMonth.getDay(); // 0 = Sunday
    
    // Calculate the first date to show (including previous month days)
    const startDate = new Date(firstDayOfMonth);
    startDate.setDate(startDate.getDate() - startingDayOfWeek);
    
    const days: CalendarDay[] = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Generate 42 days (6 weeks)
    for (let i = 0; i < 42; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      
      const isCurrentMonth = date.getMonth() === month;
      const isToday = date.getTime() === today.getTime();
      
      // Group reimbursements by this date
      const dayReimbursements = {
        expected: reimbursements.filter(r => {
          const expectedDate = new Date(r.expected_date);
          expectedDate.setHours(0, 0, 0, 0);
          return expectedDate.getTime() === date.getTime() && r.status !== 'received';
        }),
        received: reimbursements.filter(r => {
          if (!r.actual_date) return false;
          const receivedDate = new Date(r.actual_date);
          receivedDate.setHours(0, 0, 0, 0);
          return receivedDate.getTime() === date.getTime();
        })
      };
      
      days.push({
        date,
        isCurrentMonth,
        isToday,
        reimbursements: dayReimbursements
      });
    }
    
    return days;
  }, [currentDate, reimbursements]);

  const getEventCount = (day: CalendarDay) => {
    switch (viewType) {
      case 'expected':
        return day.reimbursements.expected.length;
      case 'received':
        return day.reimbursements.received.length;
      case 'both':
        return day.reimbursements.expected.length + day.reimbursements.received.length;
      default:
        return 0;
    }
  };

  const getEventColor = (day: CalendarDay) => {
    const hasExpected = day.reimbursements.expected.length > 0;
    const hasReceived = day.reimbursements.received.length > 0;
    const hasOverdue = day.reimbursements.expected.some(r => 
      new Date(r.expected_date) < new Date() && r.status !== 'received'
    );
    
    if (hasOverdue) return 'bg-red-100 border-red-300';
    if (hasExpected && hasReceived) return 'bg-purple-100 border-purple-300';
    if (hasExpected) return 'bg-yellow-100 border-yellow-300';
    if (hasReceived) return 'bg-green-100 border-green-300';
    return '';
  };

  const formatMonth = (date: Date) => {
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  };

  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <div className={`bg-white shadow rounded-lg ${className}`}>
      {/* Header */}
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Reimbursement Calendar
            </h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Track expected and received reimbursement dates
            </p>
          </div>
          <div className="flex items-center space-x-3">
            {/* View Type Selector */}
            <select
              value={viewType}
              onChange={(e) => setViewType(e.target.value as any)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="both">All Events</option>
              <option value="expected">Expected Dates</option>
              <option value="received">Received Dates</option>
            </select>
          </div>
        </div>
      </div>

      {/* Calendar Navigation */}
      <div className="px-4 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">
            {formatMonth(currentDate)}
          </h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => navigateMonth('prev')}
              className="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
            >
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button
              onClick={goToToday}
              className="px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Today
            </button>
            <button
              onClick={() => navigateMonth('next')}
              className="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
            >
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="p-4">
        {/* Week Headers */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {weekDays.map(day => (
            <div
              key={day}
              className="py-2 text-center text-sm font-medium text-gray-500"
            >
              {day}
            </div>
          ))}
        </div>

        {/* Calendar Days */}
        <div className="grid grid-cols-7 gap-1">
          {generateCalendarDays.map((day, index) => {
            const eventCount = getEventCount(day);
            const eventColor = getEventColor(day);
            
            return (
              <div
                key={index}
                className={`
                  relative min-h-[80px] p-1 border border-gray-200 rounded cursor-pointer
                  ${day.isCurrentMonth ? 'bg-white' : 'bg-gray-50'}
                  ${day.isToday ? 'ring-2 ring-blue-500' : ''}
                  ${eventColor}
                  hover:bg-gray-50 transition-colors
                `}
                onClick={() => onDateClick?.(day.date, day.reimbursements)}
              >
                {/* Date Number */}
                <div className={`text-sm ${
                  day.isCurrentMonth 
                    ? day.isToday 
                      ? 'font-semibold text-blue-600' 
                      : 'text-gray-900'
                    : 'text-gray-400'
                }`}>
                  {day.date.getDate()}
                </div>

                {/* Event Indicators */}
                {eventCount > 0 && (
                  <div className="mt-1 space-y-1">
                    {/* Expected Events */}
                    {(viewType === 'expected' || viewType === 'both') && 
                      day.reimbursements.expected.slice(0, 2).map((reimbursement, idx) => {
                        const isOverdue = new Date(reimbursement.expected_date) < new Date() && reimbursement.status !== 'received';
                        return (
                          <div
                            key={`expected-${idx}`}
                            className={`text-xs px-1 py-0.5 rounded truncate cursor-pointer ${
                              isOverdue 
                                ? 'bg-red-200 text-red-800' 
                                : 'bg-yellow-200 text-yellow-800'
                            }`}
                            onClick={(e) => {
                              e.stopPropagation();
                              onReimbursementClick?.(reimbursement);
                            }}
                            title={`Expected: ${reimbursement.reference_number} - KWD ${reimbursement.amount.toFixed(3)}`}
                          >
                            {reimbursement.reference_number}
                          </div>
                        );
                      })
                    }

                    {/* Received Events */}
                    {(viewType === 'received' || viewType === 'both') && 
                      day.reimbursements.received.slice(0, 2).map((reimbursement, idx) => (
                        <div
                          key={`received-${idx}`}
                          className="text-xs px-1 py-0.5 rounded truncate cursor-pointer bg-green-200 text-green-800"
                          onClick={(e) => {
                            e.stopPropagation();
                            onReimbursementClick?.(reimbursement);
                          }}
                          title={`Received: ${reimbursement.reference_number} - KWD ${reimbursement.amount.toFixed(3)}`}
                        >
                          ✓ {reimbursement.reference_number}
                        </div>
                      ))
                    }

                    {/* More indicator */}
                    {eventCount > 2 && (
                      <div className="text-xs text-gray-500 text-center">
                        +{eventCount - 2} more
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Legend */}
      <div className="px-4 py-3 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-yellow-200 border border-yellow-300 rounded mr-1"></div>
              <span className="text-gray-600">Expected</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-200 border border-green-300 rounded mr-1"></div>
              <span className="text-gray-600">Received</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-red-200 border border-red-300 rounded mr-1"></div>
              <span className="text-gray-600">Overdue</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-purple-200 border border-purple-300 rounded mr-1"></div>
              <span className="text-gray-600">Both</span>
            </div>
          </div>
          <div className="text-gray-500">
            Click on dates or reimbursements for details
          </div>
        </div>
      </div>
    </div>
  );
};