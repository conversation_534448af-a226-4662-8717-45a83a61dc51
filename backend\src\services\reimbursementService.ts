import { supabaseAdmin } from '../config/supabase';
import { createError } from '../middleware/errorHandler';

export interface Reimbursement {
  id: string;
  business_id: string;
  user_id: string;
  reference_number: string;
  amount: number;
  currency: 'KWD';
  description: string | null;
  request_date: string;
  expected_date: string;
  actual_date: string | null;
  status: 'pending' | 'approved' | 'received';
  created_at: string;
  updated_at: string;
}

export interface ReimbursementStatusChange {
  id: string;
  reimbursement_id: string;
  from_status: 'pending' | 'approved' | 'received';
  to_status: 'pending' | 'approved' | 'received';
  changed_by: string;
  notes: string | null;
  created_at: string;
}

export interface CreateReimbursementData {
  business_id: string;
  payment_ids: string[];
  amount?: number; // Optional - will be calculated if not provided
  description?: string;
  expected_date?: string; // Optional - will be calculated if not provided
}

export interface UpdateReimbursementData {
  amount?: number;
  description?: string;
  expected_date?: string;
  actual_date?: string;
}

export interface ReimbursementFilters {
  business_id?: string;
  status?: 'pending' | 'approved' | 'received';
  date_from?: string;
  date_to?: string;
  amount_min?: number;
  amount_max?: number;
}

export interface ReimbursementWithPayments extends Reimbursement {
  payments: {
    id: string;
    amount: number;
    currency: string;
    payment_date: string;
    subscription: {
      service_name: string;
      provider: string;
    };
  }[];
  business: {
    id: string;
    name: string;
    reimbursement_timeline: number;
  };
}

export class ReimbursementService {
  /**
   * Generate unique reference number for reimbursement
   */
  private static async generateReferenceNumber(businessId: string): Promise<string> {
    try {
      // Get business name for prefix
      const { data: business, error: businessError } = await supabaseAdmin
        .from('businesses')
        .select('name')
        .eq('id', businessId)
        .single();

      if (businessError || !business) {
        throw createError('Business not found', 404);
      }

      // Create prefix from business name (first 3 letters, uppercase)
      const prefix = business.name.replace(/[^a-zA-Z]/g, '').substring(0, 3).toUpperCase() || 'REI';
      
      // Get current year and month
      const now = new Date();
      const year = now.getFullYear().toString().substring(2); // Last 2 digits
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      
      // Get count of reimbursements this month for sequence number
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59).toISOString();
      
      const { count, error: countError } = await supabaseAdmin
        .from('reimbursements')
        .select('id', { count: 'exact' })
        .eq('business_id', businessId)
        .gte('created_at', startOfMonth)
        .lte('created_at', endOfMonth);

      if (countError) {
        throw createError(`Failed to generate reference number: ${countError.message}`, 500);
      }

      const sequence = ((count || 0) + 1).toString().padStart(3, '0');
      
      return `${prefix}${year}${month}${sequence}`;
    } catch (error) {
      console.error('Failed to generate reference number:', error);
      throw error;
    }
  }

  /**
   * Calculate expected reimbursement date based on business timeline
   */
  private static calculateExpectedDate(
    businessTimeline: number,
    requestDate: Date = new Date()
  ): string {
    const expectedDate = new Date(requestDate);
    expectedDate.setDate(expectedDate.getDate() + businessTimeline);
    return expectedDate.toISOString();
  }

  /**
   * Get reimbursements for a user with optional filters
   */
  static async getUserReimbursements(
    userId: string,
    filters: ReimbursementFilters = {},
    limit: number = 50,
    offset: number = 0
  ): Promise<{ reimbursements: ReimbursementWithPayments[]; total: number }> {
    try {
      let query = supabaseAdmin
        .from('reimbursements')
        .select(`
          *,
          businesses!inner(id, name, reimbursement_timeline, user_id),
          reimbursement_payments!inner(
            payment_id,
            payments!inner(
              id,
              amount,
              currency,
              payment_date,
              subscriptions!inner(
                service_name,
                provider
              )
            )
          )
        `, { count: 'exact' })
        .eq('businesses.user_id', userId);

      // Apply filters
      if (filters.business_id) {
        query = query.eq('business_id', filters.business_id);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.date_from) {
        query = query.gte('request_date', filters.date_from);
      }
      if (filters.date_to) {
        query = query.lte('request_date', filters.date_to);
      }
      if (filters.amount_min) {
        query = query.gte('amount', filters.amount_min);
      }
      if (filters.amount_max) {
        query = query.lte('amount', filters.amount_max);
      }

      // Apply pagination and ordering
      query = query
        .order('request_date', { ascending: false })
        .range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      if (error) {
        throw createError(`Failed to fetch reimbursements: ${error.message}`, 500);
      }

      // Transform data to include payments array
      const reimbursements = (data || []).map(reimbursement => ({
        ...reimbursement,
        business: reimbursement.businesses,
        payments: reimbursement.reimbursement_payments.map((rp: any) => ({
          id: rp.payments.id,
          amount: rp.payments.amount,
          currency: rp.payments.currency,
          payment_date: rp.payments.payment_date,
          subscription: rp.payments.subscriptions
        }))
      }));

      return {
        reimbursements,
        total: count || 0
      };
    } catch (error) {
      console.error('Failed to get user reimbursements:', error);
      throw error;
    }
  }

  /**
   * Get a specific reimbursement by ID
   */
  static async getReimbursementById(
    reimbursementId: string,
    userId: string
  ): Promise<ReimbursementWithPayments> {
    try {
      const { data, error } = await supabaseAdmin
        .from('reimbursements')
        .select(`
          *,
          businesses!inner(id, name, reimbursement_timeline, user_id),
          reimbursement_payments!inner(
            payment_id,
            payments!inner(
              id,
              amount,
              currency,
              payment_date,
              subscriptions!inner(
                service_name,
                provider
              )
            )
          )
        `)
        .eq('id', reimbursementId)
        .eq('businesses.user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          throw createError('Reimbursement not found', 404);
        }
        throw createError(`Failed to fetch reimbursement: ${error.message}`, 500);
      }

      // Transform data
      const reimbursement = {
        ...data,
        business: data.businesses,
        payments: data.reimbursement_payments.map((rp: any) => ({
          id: rp.payments.id,
          amount: rp.payments.amount,
          currency: rp.payments.currency,
          payment_date: rp.payments.payment_date,
          subscription: rp.payments.subscriptions
        }))
      };

      return reimbursement;
    } catch (error) {
      console.error('Failed to get reimbursement by ID:', error);
      throw error;
    }
  }

  /**
   * Create a new reimbursement
   */
  static async createReimbursement(
    userId: string,
    reimbursementData: CreateReimbursementData
  ): Promise<ReimbursementWithPayments> {
    try {
      // Verify business belongs to user
      const { data: business, error: businessError } = await supabaseAdmin
        .from('businesses')
        .select('id, name, reimbursement_timeline')
        .eq('id', reimbursementData.business_id)
        .eq('user_id', userId)
        .single();

      if (businessError || !business) {
        throw createError('Business not found or access denied', 404);
      }

      // Verify all payments belong to user and are associated with the business
      if (reimbursementData.payment_ids.length === 0) {
        throw createError('At least one payment must be included', 400);
      }

      const { data: payments, error: paymentsError } = await supabaseAdmin
        .from('payments')
        .select(`
          id,
          amount,
          currency,
          payment_date,
          subscriptions!inner(
            business_id,
            service_name,
            provider
          )
        `)
        .in('id', reimbursementData.payment_ids)
        .eq('subscriptions.business_id', reimbursementData.business_id);

      if (paymentsError) {
        throw createError(`Failed to validate payments: ${paymentsError.message}`, 500);
      }

      if (!payments || payments.length !== reimbursementData.payment_ids.length) {
        throw createError('Some payments not found or do not belong to the specified business', 400);
      }

      // Check if any payments are already linked to other reimbursements
      const { data: existingLinks, error: linksError } = await supabaseAdmin
        .from('reimbursement_payments')
        .select('payment_id')
        .in('payment_id', reimbursementData.payment_ids);

      if (linksError) {
        throw createError(`Failed to check existing reimbursement links: ${linksError.message}`, 500);
      }

      if (existingLinks && existingLinks.length > 0) {
        throw createError('Some payments are already linked to other reimbursements', 400);
      }

      // Calculate total amount if not provided
      const totalAmount = reimbursementData.amount || 
        payments.reduce((sum, payment) => sum + payment.amount, 0);

      // Calculate expected date if not provided
      const requestDate = new Date();
      const expectedDate = reimbursementData.expected_date || 
        this.calculateExpectedDate(business.reimbursement_timeline, requestDate);

      // Generate reference number
      const referenceNumber = await this.generateReferenceNumber(reimbursementData.business_id);

      // Create the reimbursement
      const { data: newReimbursement, error: reimbursementError } = await supabaseAdmin
        .from('reimbursements')
        .insert({
          business_id: reimbursementData.business_id,
          user_id: userId,
          reference_number: referenceNumber,
          amount: totalAmount,
          currency: 'KWD',
          description: reimbursementData.description || null,
          request_date: requestDate.toISOString(),
          expected_date: expectedDate,
          status: 'pending'
        })
        .select()
        .single();

      if (reimbursementError) {
        throw createError(`Failed to create reimbursement: ${reimbursementError.message}`, 400);
      }

      // Link payments to reimbursement
      const paymentLinks = reimbursementData.payment_ids.map(paymentId => ({
        reimbursement_id: newReimbursement.id,
        payment_id: paymentId
      }));

      const { error: linkError } = await supabaseAdmin
        .from('reimbursement_payments')
        .insert(paymentLinks);

      if (linkError) {
        // Rollback reimbursement creation
        await supabaseAdmin
          .from('reimbursements')
          .delete()
          .eq('id', newReimbursement.id);
        
        throw createError(`Failed to link payments: ${linkError.message}`, 500);
      }

      // Return the complete reimbursement with payments
      return await this.getReimbursementById(newReimbursement.id, userId);
    } catch (error) {
      console.error('Failed to create reimbursement:', error);
      throw error;
    }
  }

  /**
   * Update reimbursement status with audit trail
   */
  static async updateReimbursementStatus(
    reimbursementId: string,
    userId: string,
    newStatus: 'pending' | 'approved' | 'received',
    notes?: string
  ): Promise<ReimbursementWithPayments> {
    try {
      // Get current reimbursement
      const currentReimbursement = await this.getReimbursementById(reimbursementId, userId);
      
      if (currentReimbursement.status === newStatus) {
        throw createError('Reimbursement is already in the specified status', 400);
      }

      // Validate status transition
      const validTransitions: Record<string, string[]> = {
        pending: ['approved'],
        approved: ['received', 'pending'],
        received: [] // No transitions from received
      };

      if (!validTransitions[currentReimbursement.status].includes(newStatus)) {
        throw createError(
          `Invalid status transition from ${currentReimbursement.status} to ${newStatus}`,
          400
        );
      }

      // Update reimbursement status
      const updateData: any = { status: newStatus };
      if (newStatus === 'received') {
        updateData.actual_date = new Date().toISOString();
      }

      const { data: updatedReimbursement, error: updateError } = await supabaseAdmin
        .from('reimbursements')
        .update(updateData)
        .eq('id', reimbursementId)
        .select()
        .single();

      if (updateError) {
        throw createError(`Failed to update reimbursement status: ${updateError.message}`, 500);
      }

      // Create status change audit record
      const { error: auditError } = await supabaseAdmin
        .from('reimbursement_status_changes')
        .insert({
          reimbursement_id: reimbursementId,
          from_status: currentReimbursement.status,
          to_status: newStatus,
          changed_by: userId,
          notes: notes || null
        });

      if (auditError) {
        console.error('Failed to create audit record:', auditError);
        // Don't fail the operation, just log the error
      }

      // Return updated reimbursement
      return await this.getReimbursementById(reimbursementId, userId);
    } catch (error) {
      console.error('Failed to update reimbursement status:', error);
      throw error;
    }
  }

  /**
   * Update reimbursement details
   */
  static async updateReimbursement(
    reimbursementId: string,
    userId: string,
    updates: UpdateReimbursementData
  ): Promise<ReimbursementWithPayments> {
    try {
      // Verify reimbursement belongs to user
      await this.getReimbursementById(reimbursementId, userId);

      const { data, error } = await supabaseAdmin
        .from('reimbursements')
        .update(updates)
        .eq('id', reimbursementId)
        .select()
        .single();

      if (error) {
        throw createError(`Failed to update reimbursement: ${error.message}`, 400);
      }

      return await this.getReimbursementById(reimbursementId, userId);
    } catch (error) {
      console.error('Failed to update reimbursement:', error);
      throw error;
    }
  }

  /**
   * Delete a reimbursement
   */
  static async deleteReimbursement(reimbursementId: string, userId: string): Promise<void> {
    try {
      // Verify reimbursement belongs to user
      const reimbursement = await this.getReimbursementById(reimbursementId, userId);

      // Only allow deletion if status is pending
      if (reimbursement.status !== 'pending') {
        throw createError('Only pending reimbursements can be deleted', 400);
      }

      // Delete payment links first
      const { error: linkError } = await supabaseAdmin
        .from('reimbursement_payments')
        .delete()
        .eq('reimbursement_id', reimbursementId);

      if (linkError) {
        throw createError(`Failed to delete payment links: ${linkError.message}`, 500);
      }

      // Delete status change records
      const { error: auditError } = await supabaseAdmin
        .from('reimbursement_status_changes')
        .delete()
        .eq('reimbursement_id', reimbursementId);

      if (auditError) {
        console.error('Failed to delete audit records:', auditError);
        // Continue with deletion
      }

      // Delete the reimbursement
      const { error } = await supabaseAdmin
        .from('reimbursements')
        .delete()
        .eq('id', reimbursementId);

      if (error) {
        throw createError(`Failed to delete reimbursement: ${error.message}`, 500);
      }
    } catch (error) {
      console.error('Failed to delete reimbursement:', error);
      throw error;
    }
  }

  /**
   * Get reimbursement statistics for a business
   */
  static async getBusinessReimbursementStats(businessId: string, userId: string) {
    try {
      // Verify business belongs to user
      const { data: business, error: businessError } = await supabaseAdmin
        .from('businesses')
        .select('id')
        .eq('id', businessId)
        .eq('user_id', userId)
        .single();

      if (businessError || !business) {
        throw createError('Business not found or access denied', 404);
      }

      // Get reimbursement statistics
      const { data: reimbursements, error: reimbursementsError } = await supabaseAdmin
        .from('reimbursements')
        .select('status, amount, request_date, expected_date, actual_date')
        .eq('business_id', businessId);

      if (reimbursementsError) {
        throw createError(`Failed to get reimbursements: ${reimbursementsError.message}`, 500);
      }

      const stats = {
        total: reimbursements?.length || 0,
        pending: reimbursements?.filter(r => r.status === 'pending').length || 0,
        approved: reimbursements?.filter(r => r.status === 'approved').length || 0,
        received: reimbursements?.filter(r => r.status === 'received').length || 0,
        totalAmount: reimbursements?.reduce((sum, r) => sum + r.amount, 0) || 0,
        pendingAmount: reimbursements?.filter(r => r.status === 'pending')
          .reduce((sum, r) => sum + r.amount, 0) || 0,
        approvedAmount: reimbursements?.filter(r => r.status === 'approved')
          .reduce((sum, r) => sum + r.amount, 0) || 0,
        receivedAmount: reimbursements?.filter(r => r.status === 'received')
          .reduce((sum, r) => sum + r.amount, 0) || 0
      };

      // Calculate overdue reimbursements
      const now = new Date();
      const overdue = reimbursements?.filter(r => 
        (r.status === 'pending' || r.status === 'approved') && 
        new Date(r.expected_date) < now
      ).length || 0;

      return {
        ...stats,
        overdue
      };
    } catch (error) {
      console.error('Failed to get business reimbursement stats:', error);
      throw error;
    }
  }

  /**
   * Get reimbursement status change history
   */
  static async getReimbursementHistory(
    reimbursementId: string,
    userId: string
  ): Promise<ReimbursementStatusChange[]> {
    try {
      // Verify reimbursement belongs to user
      await this.getReimbursementById(reimbursementId, userId);

      const { data, error } = await supabaseAdmin
        .from('reimbursement_status_changes')
        .select('*')
        .eq('reimbursement_id', reimbursementId)
        .order('created_at', { ascending: false });

      if (error) {
        throw createError(`Failed to fetch reimbursement history: ${error.message}`, 500);
      }

      return data || [];
    } catch (error) {
      console.error('Failed to get reimbursement history:', error);
      throw error;
    }
  }

  /**
   * Search reimbursements
   */
  static async searchReimbursements(
    userId: string,
    searchTerm: string,
    filters: ReimbursementFilters = {}
  ): Promise<ReimbursementWithPayments[]> {
    try {
      let query = supabaseAdmin
        .from('reimbursements')
        .select(`
          *,
          businesses!inner(id, name, reimbursement_timeline, user_id),
          reimbursement_payments!inner(
            payment_id,
            payments!inner(
              id,
              amount,
              currency,
              payment_date,
              subscriptions!inner(
                service_name,
                provider
              )
            )
          )
        `)
        .eq('businesses.user_id', userId)
        .or(`reference_number.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);

      // Apply filters
      if (filters.business_id) {
        query = query.eq('business_id', filters.business_id);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      query = query.order('request_date', { ascending: false });

      const { data, error } = await query;

      if (error) {
        throw createError(`Failed to search reimbursements: ${error.message}`, 500);
      }

      // Transform data
      const reimbursements = (data || []).map(reimbursement => ({
        ...reimbursement,
        business: reimbursement.businesses,
        payments: reimbursement.reimbursement_payments.map((rp: any) => ({
          id: rp.payments.id,
          amount: rp.payments.amount,
          currency: rp.payments.currency,
          payment_date: rp.payments.payment_date,
          subscription: rp.payments.subscriptions
        }))
      }));

      return reimbursements;
    } catch (error) {
      console.error('Failed to search reimbursements:', error);
      throw error;
    }
  }
}