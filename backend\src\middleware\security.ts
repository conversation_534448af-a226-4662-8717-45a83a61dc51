import { Request, Response, NextFunction } from 'express';
import { config } from '../config/config';

/**
 * Input sanitization middleware to prevent XSS and injection attacks
 */
export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  const sanitizeValue = (value: any): any => {
    if (typeof value === 'string') {
      // Remove potentially dangerous characters and HTML tags
      return value
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/<[^>]*>/g, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
        .trim();
    }
    
    if (Array.isArray(value)) {
      return value.map(sanitizeValue);
    }
    
    if (value && typeof value === 'object') {
      const sanitizedObj: any = {};
      for (const [key, val] of Object.entries(value)) {
        sanitizedObj[key] = sanitizeValue(val);
      }
      return sanitizedObj;
    }
    
    return value;
  };

  if (req.body) {
    req.body = sanitizeValue(req.body);
  }
  
  if (req.query) {
    req.query = sanitizeValue(req.query);
  }
  
  if (req.params) {
    req.params = sanitizeValue(req.params);
  }

  next();
};

/**
 * Security headers middleware for additional protection
 */
export const securityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // Prevent clickjacking
  res.setHeader('X-Frame-Options', 'DENY');
  
  // Prevent MIME type sniffing
  res.setHeader('X-Content-Type-Options', 'nosniff');
  
  // Enable XSS protection
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // Referrer policy
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Permissions policy
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  
  // Remove server information
  res.removeHeader('X-Powered-By');
  
  next();
};

/**
 * Request logging middleware for security monitoring
 */
export const securityLogging = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  
  // Log suspicious patterns
  const suspiciousPatterns = [
    /\.\.\//g, // Path traversal
    /<script/gi, // XSS attempts
    /union.*select/gi, // SQL injection
    /exec\(/gi, // Code injection
    /eval\(/gi, // Code injection
    /javascript:/gi, // XSS
  ];
  
  const fullUrl = req.protocol + '://' + req.get('host') + req.originalUrl;
  const userAgent = req.get('User-Agent') || '';
  const reqBody = JSON.stringify(req.body);
  
  const isSuspicious = suspiciousPatterns.some(pattern => 
    pattern.test(fullUrl) || 
    pattern.test(userAgent) || 
    pattern.test(reqBody)
  );
  
  if (isSuspicious) {
    console.warn(`🚨 SUSPICIOUS REQUEST DETECTED:`, {
      timestamp: new Date().toISOString(),
      ip: req.ip,
      method: req.method,
      url: req.originalUrl,
      userAgent,
      body: req.body
    });
  }
  
  // Log response time for performance monitoring
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    
    if (duration > 5000) { // Log slow requests (>5s)
      console.warn(`🐌 SLOW REQUEST:`, {
        timestamp: new Date().toISOString(),
        method: req.method,
        url: req.originalUrl,
        duration: `${duration}ms`,
        status: res.statusCode
      });
    }
    
    // Log errors for security monitoring
    if (res.statusCode >= 400) {
      console.error(`❌ ERROR RESPONSE:`, {
        timestamp: new Date().toISOString(),
        ip: req.ip,
        method: req.method,
        url: req.originalUrl,
        status: res.statusCode,
        userAgent
      });
    }
  });
  
  next();
};

/**
 * File upload security middleware
 */
export const fileUploadSecurity = (req: Request, res: Response, next: NextFunction) => {
  // Additional file upload validation can be added here
  // This is called after multer processing
  
  if (req.files) {
    const files = Array.isArray(req.files) ? req.files : Object.values(req.files).flat();
    
    for (const file of files) {
      // Check for malicious file names
      const fileName = (file as any).originalname || '';
      const suspiciousPatterns = [
        /\.\./g,
        /[<>:"|?*]/g,
        /\.php$/i,
        /\.exe$/i,
        /\.bat$/i,
        /\.cmd$/i,
        /\.scr$/i
      ];
      
      if (suspiciousPatterns.some(pattern => pattern.test(fileName))) {
        return res.status(400).json({
          success: false,
          message: 'Invalid file name or type'
        });
      }
      
      // Additional MIME type validation
      const allowedTypes = config.ALLOWED_FILE_TYPES.split(',');
      if (!allowedTypes.includes((file as any).mimetype)) {
        return res.status(400).json({
          success: false,
          message: 'File type not allowed'
        });
      }
    }
  }
  
  next();
};

/**
 * Development security middleware - only in development
 */
export const devSecurityMiddleware = (req: Request, res: Response, next: NextFunction) => {
  if (config.NODE_ENV === 'development') {
    // Add development-specific security logging
    console.log(`🔍 DEV REQUEST: ${req.method} ${req.originalUrl}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString()
    });
  }
  next();
};

/**
 * Production security middleware
 */
export const prodSecurityMiddleware = (req: Request, res: Response, next: NextFunction) => {
  if (config.NODE_ENV === 'production') {
    // Additional production security measures
    
    // Block requests with suspicious headers
    const suspiciousHeaders = ['x-forwarded-host', 'x-original-url', 'x-rewrite-url'];
    for (const header of suspiciousHeaders) {
      if (req.headers[header]) {
        console.warn(`🚨 SUSPICIOUS HEADER DETECTED: ${header}`, {
          ip: req.ip,
          value: req.headers[header]
        });
        return res.status(400).json({ error: 'Invalid request' });
      }
    }
    
    // Validate Content-Type for POST/PUT requests
    if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
      const contentType = req.headers['content-type'];
      if (contentType && !contentType.includes('application/json') && !contentType.includes('multipart/form-data')) {
        return res.status(415).json({ error: 'Unsupported media type' });
      }
    }
  }
  next();
};

/**
 * API versioning and deprecation middleware
 */
export const apiVersioning = (req: Request, res: Response, next: NextFunction) => {
  // Add API version headers
  res.setHeader('API-Version', '1.0.0');
  res.setHeader('API-Supported-Versions', '1.0.0');
  
  // Future: Handle API versioning logic here
  next();
};