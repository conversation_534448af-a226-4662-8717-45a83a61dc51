-- Create subscription_categories table
CREATE TABLE IF NOT EXISTS public.subscription_categories (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  color TEXT NOT NULL CHECK (color ~ '^#[0-9A-Fa-f]{6}$'),
  icon TEXT,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, name)
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_subscription_categories_user_id ON public.subscription_categories(user_id);

-- Enable RLS
ALTER TABLE public.subscription_categories ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view own categories" ON public.subscription_categories
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own categories" ON public.subscription_categories
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own categories" ON public.subscription_categories
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own categories" ON public.subscription_categories
  FOR DELETE USING (auth.uid() = user_id);

-- Insert default categories
INSERT INTO public.subscription_categories (user_id, name, color, icon, description)
SELECT 
  u.id,
  category.name,
  category.color,
  category.icon,
  category.description
FROM public.users u
CROSS JOIN (
  VALUES 
    ('Entertainment', '#FF6B6B', '🎬', 'Streaming services, games, and entertainment'),
    ('Software', '#4ECDC4', '💻', 'Software subscriptions and tools'),
    ('Utilities', '#45B7D1', '⚡', 'Internet, phone, and utility services'),
    ('Business', '#96CEB4', '💼', 'Business and professional services'),
    ('Health', '#FFEAA7', '🏥', 'Health and fitness subscriptions'),
    ('Education', '#DDA0DD', '📚', 'Learning and educational platforms'),
    ('News', '#74B9FF', '📰', 'News and media subscriptions'),
    ('Cloud Storage', '#95A5A6', '☁️', 'Cloud storage and backup services')
) AS category(name, color, icon, description)
ON CONFLICT (user_id, name) DO NOTHING;

-- Update trigger for updated_at
CREATE OR REPLACE FUNCTION update_subscription_categories_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER subscription_categories_updated_at
  BEFORE UPDATE ON public.subscription_categories
  FOR EACH ROW
  EXECUTE FUNCTION update_subscription_categories_updated_at();
