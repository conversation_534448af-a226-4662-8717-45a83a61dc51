
import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const profileSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address'),
});

type ProfileFormData = z.infer<typeof profileSchema>;

const passwordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, 'New password must be at least 8 characters'),
});

type PasswordFormData = z.infer<typeof passwordSchema>;

export const ProfilePage: React.FC = () => {
  const { profile, updateProfile, changePassword, deleteAccount, loading } = useAuth();
  const [updateError, setUpdateError] = useState<string | null>(null);
  const [passwordError, setPasswordError] = useState<string | null>(null);

  const { register: registerProfile, handleSubmit: handleSubmitProfile, formState: { errors: profileErrors } } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      firstName: profile?.first_name || '',
      lastName: profile?.last_name || '',
      email: profile?.email || '',
    },
  });

  const { register: registerPassword, handleSubmit: handleSubmitPassword, formState: { errors: passwordErrors }, reset: resetPasswordForm } = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema),
  });

  const onProfileSubmit = async (data: ProfileFormData) => {
    setUpdateError(null);
    const { error } = await updateProfile({ first_name: data.firstName, last_name: data.lastName, email: data.email });
    if (error) {
      setUpdateError(error.message);
    }
  };

  const onPasswordSubmit = async (data: PasswordFormData) => {
    setPasswordError(null);
    const { error } = await changePassword(data.currentPassword, data.newPassword);
    if (error) {
      setPasswordError(error.message);
    } else {
      resetPasswordForm();
    }
  };

  const handleDeleteAccount = async () => {
    if (window.confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
      await deleteAccount();
    }
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">My Profile</h1>

      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium mb-4">Profile Information</h2>
        <form onSubmit={handleSubmitProfile(onProfileSubmit)} className="space-y-4">
          {updateError && <p className="text-red-600">{updateError}</p>}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">First Name</label>
              <input {...registerProfile('firstName')} className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md" />
              {profileErrors.firstName && <p className="text-red-600">{profileErrors.firstName.message}</p>}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Last Name</label>
              <input {...registerProfile('lastName')} className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md" />
              {profileErrors.lastName && <p className="text-red-600">{profileErrors.lastName.message}</p>}
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Email</label>
            <input {...registerProfile('email')} type="email" className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md" />
            {profileErrors.email && <p className="text-red-600">{profileErrors.email.message}</p>}
          </div>
          <button type="submit" disabled={loading} className="bg-blue-600 text-white px-4 py-2 rounded-md">Update Profile</button>
        </form>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium mb-4">Change Password</h2>
        <form onSubmit={handleSubmitPassword(onPasswordSubmit)} className="space-y-4">
          {passwordError && <p className="text-red-600">{passwordError}</p>}
          <div>
            <label className="block text-sm font-medium text-gray-700">Current Password</label>
            <input {...registerPassword('currentPassword')} type="password" className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md" />
            {passwordErrors.currentPassword && <p className="text-red-600">{passwordErrors.currentPassword.message}</p>}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">New Password</label>
            <input {...registerPassword('newPassword')} type="password" className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md" />
            {passwordErrors.newPassword && <p className="text-red-600">{passwordErrors.newPassword.message}</p>}
          </div>
          <button type="submit" disabled={loading} className="bg-blue-600 text-white px-4 py-2 rounded-md">Change Password</button>
        </form>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium mb-4">Delete Account</h2>
        <p className="text-sm text-gray-600 mb-4">Permanently delete your account and all associated data.</p>
        <button onClick={handleDeleteAccount} disabled={loading} className="bg-red-600 text-white px-4 py-2 rounded-md">Delete Account</button>
      </div>
    </div>
  );
};
