import React, { useState, useEffect } from 'react';
import { useSubscription } from '../../contexts/SubscriptionContext';
import { useBusiness } from '../../contexts/BusinessContext';
import { useExchangeRate } from '../../contexts/ExchangeRateContext';

interface SubscriptionStats {
  totalSubscriptions: number;
  activeSubscriptions: number;
  inactiveSubscriptions: number;
  cancelledSubscriptions: number;
  totalMonthlyCost: number;
  totalYearlyCost: number;
  upcomingPayments: number;
  overduePayments: number;
  costByCategory: Record<string, number>;
  costByCurrency: Record<string, number>;
  averageSubscriptionCost: number;
}

export const SubscriptionDashboard: React.FC = () => {
  const { subscriptions, loading, upcomingPayments } = useSubscription();
  const { currentBusiness } = useBusiness();
  const { convertCurrency } = useExchangeRate();
  const [stats, setStats] = useState<SubscriptionStats | null>(null);
  const [calculating, setCalculating] = useState(false);

  useEffect(() => {
    calculateStats();
  }, [subscriptions, currentBusiness]);

  const calculateStats = async () => {
    if (!subscriptions.length) {
      setStats(null);
      return;
    }

    setCalculating(true);
    try {
      const now = new Date();
      const activeSubscriptions = subscriptions.filter(s => s.status === 'active');
      const inactiveSubscriptions = subscriptions.filter(s => s.status === 'inactive');
      const cancelledSubscriptions = subscriptions.filter(s => s.status === 'cancelled');

      // Calculate costs in KWD
      let totalMonthlyCost = 0;
      let totalYearlyCost = 0;
      const costByCategory: Record<string, number> = {};
      const costByCurrency: Record<string, number> = {};

      for (const subscription of activeSubscriptions) {
        let monthlyCostKWD = 0;
        
        if (subscription.currency === 'KWD') {
          monthlyCostKWD = subscription.amount;
        } else {
          // Convert to KWD
          const conversion = await convertCurrency(subscription.amount, subscription.currency, 'KWD');
          monthlyCostKWD = conversion?.convertedAmount || 0;
        }

        // Adjust for billing frequency
        switch (subscription.billing_frequency) {
          case 'monthly':
            // Already monthly
            break;
          case 'quarterly':
            monthlyCostKWD = monthlyCostKWD / 3;
            break;
          case 'yearly':
            monthlyCostKWD = monthlyCostKWD / 12;
            break;
        }

        totalMonthlyCost += monthlyCostKWD;
        totalYearlyCost += monthlyCostKWD * 12;

        // Category breakdown
        const category = subscription.category || 'Uncategorized';
        costByCategory[category] = (costByCategory[category] || 0) + monthlyCostKWD;

        // Currency breakdown
        costByCurrency[subscription.currency] = (costByCurrency[subscription.currency] || 0) + subscription.amount;
      }

      // Calculate upcoming and overdue payments
      const upcomingCount = upcomingPayments.filter(s => {
        const paymentDate = new Date(s.next_payment_date);
        const daysUntilPayment = Math.ceil((paymentDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
        return daysUntilPayment >= 0 && daysUntilPayment <= 30;
      }).length;

      const overdueCount = upcomingPayments.filter(s => {
        const paymentDate = new Date(s.next_payment_date);
        return paymentDate < now;
      }).length;

      const averageSubscriptionCost = activeSubscriptions.length > 0 ? totalMonthlyCost / activeSubscriptions.length : 0;

      setStats({
        totalSubscriptions: subscriptions.length,
        activeSubscriptions: activeSubscriptions.length,
        inactiveSubscriptions: inactiveSubscriptions.length,
        cancelledSubscriptions: cancelledSubscriptions.length,
        totalMonthlyCost,
        totalYearlyCost,
        upcomingPayments: upcomingCount,
        overduePayments: overdueCount,
        costByCategory,
        costByCurrency,
        averageSubscriptionCost
      });
    } catch (error) {
      console.error('Error calculating subscription stats:', error);
    } finally {
      setCalculating(false);
    }
  };

  if (loading || calculating) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Subscription Data</h3>
          <p className="text-gray-500">Add some subscriptions to see analytics.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Subscriptions</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.totalSubscriptions}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Subscriptions</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.activeSubscriptions}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Monthly Cost</p>
              <p className="text-2xl font-semibold text-gray-900">KWD {stats.totalMonthlyCost.toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-100 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Overdue Payments</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.overduePayments}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Cost by Category */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Cost by Category</h3>
          <div className="space-y-3">
            {Object.entries(stats.costByCategory).map(([category, cost]) => (
              <div key={category} className="flex justify-between items-center">
                <span className="text-sm text-gray-600">{category}</span>
                <span className="text-sm font-medium text-gray-900">KWD {cost.toFixed(2)}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Cost by Currency */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Cost by Currency</h3>
          <div className="space-y-3">
            {Object.entries(stats.costByCurrency).map(([currency, cost]) => (
              <div key={currency} className="flex justify-between items-center">
                <span className="text-sm text-gray-600">{currency}</span>
                <span className="text-sm font-medium text-gray-900">{currency} {cost.toFixed(2)}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <p className="text-2xl font-semibold text-blue-600">KWD {stats.totalYearlyCost.toFixed(2)}</p>
            <p className="text-sm text-gray-500">Yearly Cost</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-semibold text-green-600">KWD {stats.averageSubscriptionCost.toFixed(2)}</p>
            <p className="text-sm text-gray-500">Average Monthly Cost</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-semibold text-orange-600">{stats.upcomingPayments}</p>
            <p className="text-sm text-gray-500">Upcoming Payments (30 days)</p>
          </div>
        </div>
      </div>
    </div>
  );
};
