# Subscription Tracker Web App - Specifications Document

## 1. Project Overview

### 1.1 Purpose
A web application to track subscription payments and reimbursements for multiple businesses, handling currency conversions between USD/GBP and KWD.

### 1.2 Core Problem
- Managing monthly subscriptions for 3 different businesses
- Tracking payment status and reimbursement status
- Handling multi-currency transactions (USD/GBP to KWD)

## 2. Functional Requirements

### 2.1 Business Management
- **Add/Edit/Delete Businesses**
  - Business name
  - Business ID/Reference
  - Contact information
  - Default reimbursement timeline

### 2.2 Subscription Management
- **Add/Edit/Delete Subscriptions**
  - Subscription name
  - Service provider
  - Associated business
  - Billing frequency (monthly/quarterly/annual)
  - Amount and currency (USD/GBP)
  - Due date
  - Auto-renewal status
  - Category (Software, Services, etc.)

### 2.3 Payment Tracking
- **Record Payments**
  - Payment date
  - Amount paid (USD/GBP)
  - Exchange rate used
  - KWD equivalent
  - Payment method
  - Receipt/invoice upload
  - Notes

### 2.4 Reimbursement Tracking
- **Reimbursement Records**
  - Request submission date
  - Expected reimbursement date
  - Actual reimbursement date
  - Amount in KWD
  - Status (Pending/Approved/Received)
  - Reference number

### 2.5 Dashboard Features
- **Overview Cards**
  - Total subscriptions active
  - Upcoming payments (next 30 days)
  - Pending reimbursements
  - Total spent this month
  - Outstanding reimbursements

### 2.6 Reporting
- **Monthly Reports**
  - Payments by business
  - Currency conversion summary
  - Reimbursement status report
  - Cash flow analysis
- **Export Options**
  - PDF reports
  - CSV/Excel export

## 3. Technical Specifications

### 3.1 Technology Stack
- **Frontend**: React.js with TypeScript
- **Backend**: Node.js with Express
- **Database**: Supabase 
- **Authentication**: Supabase
- **Currency API**: Integration with real-time exchange rate API (following the setup in "https://github.com/fawazahmed0/exchange-api.git")

### 3.2 Database Schema

#### Users Table
```
- user_id (PK)
- email
- password_hash
- name
- created_at
- last_login
```

#### Businesses Table
```
- business_id (PK)
- user_id (FK)
- business_name
- contact_email
- reimbursement_timeline_days
- created_at
```

#### Subscriptions Table
```
- subscription_id (PK)
- business_id (FK)
- service_name
- provider
- amount
- currency (USD/GBP)
- billing_frequency
- next_due_date
- category
- is_active
```

#### Payments Table
```
- payment_id (PK)
- subscription_id (FK)
- payment_date
- amount_original
- currency_original
- exchange_rate
- amount_kwd
- payment_method
- receipt_url
- notes
```

#### Reimbursements Table
```
- reimbursement_id (PK)
- payment_id (FK)
- business_id (FK)
- request_date
- expected_date
- received_date
- amount_kwd
- status
- reference_number
```

### 3.3 API Endpoints

#### Authentication
- POST `/api/auth/login`
- POST `/api/auth/logout`
- POST `/api/auth/refresh`

#### Businesses
- GET `/api/businesses`
- POST `/api/businesses`
- PUT `/api/businesses/:id`
- DELETE `/api/businesses/:id`

#### Subscriptions
- GET `/api/subscriptions`
- GET `/api/subscriptions/upcoming`
- POST `/api/subscriptions`
- PUT `/api/subscriptions/:id`
- DELETE `/api/subscriptions/:id`

#### Payments
- GET `/api/payments`
- POST `/api/payments`
- PUT `/api/payments/:id`

#### Reimbursements
- GET `/api/reimbursements`
- POST `/api/reimbursements`
- PUT `/api/reimbursements/:id`

#### Reports
- GET `/api/reports/monthly/:month/:year`
- GET `/api/reports/business/:businessId`

## 4. User Interface Requirements

### 4.1 Pages
1. **Login/Register**
2. **Dashboard** - Overview with key metrics
3. **Subscriptions** - List and manage
4. **Payments** - Record and view
5. **Reimbursements** - Track status
6. **Reports** - Generate and export
7. **Settings** - User preferences, currency settings

### 4.2 Key UI Components
- **Subscription Card**
  - Service name and logo
  - Next payment due
  - Amount in original currency
  - Quick actions (Pay, View history)

- **Payment Form**
  - Subscription dropdown
  - Amount and currency
  - Auto-fetch exchange rate
  - Receipt upload
  - Calculate KWD equivalent

- **Reimbursement Tracker**
  - Timeline view
  - Status indicators
  - Days pending counter

## 5. Business Logic

### 5.1 Currency Conversion
- Fetch daily exchange rates at midnight Kuwait time
- Store historical rates for reporting
- Allow manual rate override with justification

### 5.2 Notifications
- Email alerts 7 days before subscription due
- Daily digest of pending reimbursements
- Monthly payment summary

### 5.3 Automation
- Auto-create payment records for recurring subscriptions
- Calculate expected reimbursement dates
- Flag overdue reimbursements

## 6. Security Requirements

- HTTPS only
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- Rate limiting on API endpoints
- Secure file upload for receipts
- Encryption for sensitive data

## 7. Performance Requirements

- Page load time < 2 seconds
- API response time < 500ms
- Support 100 concurrent users
- 99.9% uptime

## 8. Future Enhancements

1. Mobile app (React Native)
2. Integration with accounting software
3. Multi-user support per business
4. Automated receipt scanning (OCR)
5. Budget alerts and forecasting
6. Integration with payment gateways

## 9. Acceptance Criteria

- Successfully track subscriptions for 3 businesses
- Accurate currency conversion USD/GBP to KWD
- Complete payment and reimbursement lifecycle
- Generate monthly reports
- Export data for accounting purposes
- Responsive design for desktop and tablet