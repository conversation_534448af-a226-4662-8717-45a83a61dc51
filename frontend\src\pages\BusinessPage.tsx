import React, { useState } from 'react';
import { Business, useBusiness } from '../contexts/BusinessContext';
import { BusinessList } from '../components/business/BusinessList';
import { BusinessModal } from '../components/business/BusinessModal';
import { DeleteBusinessModal } from '../components/business/DeleteBusinessModal';
import { BusinessSelector } from '../components/business/BusinessSelector';

export const BusinessPage: React.FC = () => {
  const { createBusiness, updateBusiness, deleteBusiness } = useBusiness();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedBusiness, setSelectedBusiness] = useState<Business | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleCreateBusiness = async (data: any) => {
    setIsLoading(true);
    try {
      await createBusiness(data);
      setIsCreateModalOpen(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditBusiness = async (data: any) => {
    if (!selectedBusiness) return;
    
    setIsLoading(true);
    try {
      await updateBusiness(selectedBusiness.id, data);
      setIsEditModalOpen(false);
      setSelectedBusiness(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteBusiness = async (business: Business) => {
    await deleteBusiness(business.id);
    setIsDeleteModalOpen(false);
    setSelectedBusiness(null);
  };

  const openEditModal = (business: Business) => {
    setSelectedBusiness(business);
    setIsEditModalOpen(true);
  };

  const openDeleteModal = (business: Business) => {
    setSelectedBusiness(business);
    setIsDeleteModalOpen(true);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="md:flex md:items-center md:justify-between mb-8">
        <div className="flex-1 min-w-0">
          <h1 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Business Management
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your businesses and their settings
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <button
            onClick={() => setIsCreateModalOpen(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Business
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Business List */}
        <div className="lg:col-span-2">
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Your Businesses
              </h3>
              <BusinessList
                onEditBusiness={openEditModal}
                onDeleteBusiness={openDeleteModal}
                showActions={true}
              />
            </div>
          </div>
        </div>

        {/* Business Selector & Quick Actions */}
        <div className="space-y-6">
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Current Business
              </h3>
              <BusinessSelector className="mb-4" />
              <div className="space-y-2">
                <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md">
                  View Subscriptions
                </button>
                <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md">
                  View Payments
                </button>
                <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md">
                  View Reimbursements
                </button>
              </div>
            </div>
          </div>

          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Quick Stats
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Total Businesses</span>
                  <span className="text-sm font-medium text-gray-900">-</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Active Subscriptions</span>
                  <span className="text-sm font-medium text-gray-900">-</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Pending Reimbursements</span>
                  <span className="text-sm font-medium text-gray-900">-</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      <BusinessModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateBusiness}
        isLoading={isLoading}
        title="Create New Business"
      />

      <BusinessModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedBusiness(null);
        }}
        business={selectedBusiness || undefined}
        onSubmit={handleEditBusiness}
        isLoading={isLoading}
        title="Edit Business"
      />

      <DeleteBusinessModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setSelectedBusiness(null);
        }}
        business={selectedBusiness}
        onConfirm={handleDeleteBusiness}
      />
    </div>
  );
};