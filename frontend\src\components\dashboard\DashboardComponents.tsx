import React from 'react';

// Base card component for consistent styling
interface CardProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

export const Card: React.FC<CardProps> = ({ 
  children, 
  className = '', 
  padding = 'md' 
}) => {
  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  };

  return (
    <div className={`bg-white shadow rounded-lg ${paddingClasses[padding]} ${className}`}>
      {children}
    </div>
  );
};

// Metric card for KPIs
interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'gray';
  trend?: {
    value: number;
    label: string;
    isPositive: boolean;
  };
  loading?: boolean;
  onClick?: () => void;
  format?: 'number' | 'currency' | 'percentage';
  currency?: string;
  badge?: {
    text: string;
    variant: 'success' | 'warning' | 'danger' | 'info';
  };
}

export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  color = 'blue',
  trend,
  loading = false,
  onClick,
  format = 'number',
  currency = 'KWD',
  badge
}) => {
  const colorClasses = {
    blue: 'text-blue-600 bg-blue-100',
    green: 'text-green-600 bg-green-100',
    yellow: 'text-yellow-600 bg-yellow-100',
    red: 'text-red-600 bg-red-100',
    purple: 'text-purple-600 bg-purple-100',
    gray: 'text-gray-600 bg-gray-100'
  };

  return (
    <Card className={onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''}>
      <div onClick={onClick}>
        <div className="flex items-center">
          {icon && (
            <div className="flex-shrink-0">
              <div className={`h-10 w-10 rounded-md flex items-center justify-center ${colorClasses[color]}`}>
                {icon}
              </div>
            </div>
          )}
          <div className={`${icon ? 'ml-4' : ''} w-0 flex-1`}>
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
              <dd className="text-2xl font-semibold text-gray-900">
                {loading ? (
                  <div className="animate-pulse h-8 bg-gray-200 rounded w-16"></div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <span>
                      {format === 'currency' ? `${currency} ${typeof value === 'number' ? value.toFixed(3) : value}` :
                       format === 'percentage' ? `${value}%` : value}
                    </span>
                    {badge && (
                      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                        badge.variant === 'success' ? 'bg-green-100 text-green-800' :
                        badge.variant === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                        badge.variant === 'danger' ? 'bg-red-100 text-red-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {badge.text}
                      </span>
                    )}
                  </div>
                )}
              </dd>
              {subtitle && (
                <dd className="text-sm text-gray-500">{subtitle}</dd>
              )}
            </dl>
          </div>
        </div>
        {trend && !loading && (
          <div className="mt-4 flex items-center">
            <div className={`flex items-center text-sm ${
              trend.isPositive ? 'text-green-600' : 'text-red-600'
            }`}>
              {trend.isPositive ? (
                <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
                </svg>
              ) : (
                <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 7l-9.2 9.2M7 7v10h10" />
                </svg>
              )}
              {Math.abs(trend.value)}% {trend.label}
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

// Advanced KPI card with multiple data points
interface KPICardProps {
  title: string;
  primaryValue: {
    label: string;
    value: string | number;
    format?: 'number' | 'currency' | 'percentage';
    currency?: string;
  };
  secondaryValues?: {
    label: string;
    value: string | number;
    format?: 'number' | 'currency' | 'percentage';
    currency?: string;
  }[];
  trend?: {
    value: number;
    label: string;
    isPositive: boolean;
    period?: string;
  };
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'gray';
  icon?: React.ReactNode;
  loading?: boolean;
  onClick?: () => void;
  alert?: {
    message: string;
    type: 'warning' | 'error' | 'info';
  };
}

export const KPICard: React.FC<KPICardProps> = ({
  title,
  primaryValue,
  secondaryValues,
  trend,
  color = 'blue',
  icon,
  loading = false,
  onClick,
  alert
}) => {
  const colorClasses = {
    blue: 'text-blue-600 bg-blue-100',
    green: 'text-green-600 bg-green-100',
    yellow: 'text-yellow-600 bg-yellow-100',
    red: 'text-red-600 bg-red-100',
    purple: 'text-purple-600 bg-purple-100',
    gray: 'text-gray-600 bg-gray-100'
  };

  const formatValue = (value: string | number, format: string = 'number', currency: string = 'KWD') => {
    if (format === 'currency') {
      return `${currency} ${typeof value === 'number' ? value.toFixed(3) : value}`;
    }
    if (format === 'percentage') {
      return `${value}%`;
    }
    return value;
  };

  return (
    <Card className={onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''}>
      <div onClick={onClick}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          {icon && (
            <div className={`h-8 w-8 rounded-md flex items-center justify-center ${colorClasses[color]}`}>
              {icon}
            </div>
          )}
        </div>
        
        <div className="space-y-3">
          {/* Primary Value */}
          <div>
            <dt className="text-xs text-gray-500 uppercase tracking-wide">
              {primaryValue.label}
            </dt>
            <dd className="text-2xl font-bold text-gray-900">
              {loading ? (
                <div className="animate-pulse h-8 bg-gray-200 rounded w-20"></div>
              ) : (
                formatValue(primaryValue.value, primaryValue.format, primaryValue.currency)
              )}
            </dd>
          </div>
          
          {/* Secondary Values */}
          {secondaryValues && secondaryValues.length > 0 && (
            <div className="grid grid-cols-2 gap-2">
              {secondaryValues.map((item, index) => (
                <div key={index}>
                  <dt className="text-xs text-gray-500">{item.label}</dt>
                  <dd className="text-sm font-medium text-gray-900">
                    {loading ? (
                      <div className="animate-pulse h-4 bg-gray-200 rounded w-12"></div>
                    ) : (
                      formatValue(item.value, item.format, item.currency)
                    )}
                  </dd>
                </div>
              ))}
            </div>
          )}
          
          {/* Trend */}
          {trend && !loading && (
            <div className="flex items-center justify-between">
              <div className={`flex items-center text-sm ${
                trend.isPositive ? 'text-green-600' : 'text-red-600'
              }`}>
                {trend.isPositive ? (
                  <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
                  </svg>
                ) : (
                  <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 7l-9.2 9.2M7 7v10h10" />
                  </svg>
                )}
                {Math.abs(trend.value)}% {trend.label}
              </div>
              {trend.period && (
                <span className="text-xs text-gray-500">{trend.period}</span>
              )}
            </div>
          )}
          
          {/* Alert */}
          {alert && (
            <div className={`p-2 rounded text-xs ${
              alert.type === 'warning' ? 'bg-yellow-50 text-yellow-800' :
              alert.type === 'error' ? 'bg-red-50 text-red-800' :
              'bg-blue-50 text-blue-800'
            }`}>
              {alert.message}
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

// Loading skeleton for cards
export const CardSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => (
  <Card className={className}>
    <div className="animate-pulse">
      <div className="flex items-center">
        <div className="h-10 w-10 bg-gray-200 rounded-md"></div>
        <div className="ml-4 flex-1">
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
        </div>
      </div>
    </div>
  </Card>
);

// Section header component
interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  action?: React.ReactNode;
  className?: string;
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  subtitle,
  action,
  className = ''
}) => (
  <div className={`flex items-center justify-between ${className}`}>
    <div>
      <h2 className="text-lg font-medium text-gray-900">{title}</h2>
      {subtitle && <p className="mt-1 text-sm text-gray-500">{subtitle}</p>}
    </div>
    {action && <div>{action}</div>}
  </div>
);

// Quick action button component
interface QuickActionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  href?: string;
  onClick?: () => void;
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple';
}

export const QuickAction: React.FC<QuickActionProps> = ({
  title,
  description,
  icon,
  href,
  onClick,
  color = 'blue'
}) => {
  const colorClasses = {
    blue: 'text-blue-600 bg-blue-100 hover:bg-blue-200',
    green: 'text-green-600 bg-green-100 hover:bg-green-200',
    yellow: 'text-yellow-600 bg-yellow-100 hover:bg-yellow-200',
    red: 'text-red-600 bg-red-100 hover:bg-red-200',
    purple: 'text-purple-600 bg-purple-100 hover:bg-purple-200'
  };

  const Component = href ? 'a' : 'button';
  const props = href ? { href } : { onClick };

  return (
    <Component
      {...props}
      className="block w-full text-left p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
    >
      <div className="flex items-start">
        <div className={`flex-shrink-0 h-10 w-10 rounded-md flex items-center justify-center ${colorClasses[color]}`}>
          {icon}
        </div>
        <div className="ml-4">
          <h3 className="text-sm font-medium text-gray-900">{title}</h3>
          <p className="mt-1 text-sm text-gray-500">{description}</p>
        </div>
      </div>
    </Component>
  );
};

// Empty state component
interface EmptyStateProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  action?: React.ReactNode;
  className?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  icon,
  action,
  className = ''
}) => (
  <div className={`text-center py-12 ${className}`}>
    {icon && (
      <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
        {icon}
      </div>
    )}
    <h3 className="mt-2 text-sm font-medium text-gray-900">{title}</h3>
    <p className="mt-1 text-sm text-gray-500">{description}</p>
    {action && <div className="mt-6">{action}</div>}
  </div>
);

// Stats grid component
interface StatsGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3 | 4;
  className?: string;
}

export const StatsGrid: React.FC<StatsGridProps> = ({
  children,
  columns = 4,
  className = ''
}) => {
  const gridClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  };

  return (
    <div className={`grid ${gridClasses[columns]} gap-4 ${className}`}>
      {children}
    </div>
  );
};

// Chart container with loading state
interface ChartContainerProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  loading?: boolean;
  error?: string;
  action?: React.ReactNode;
  className?: string;
}

export const ChartContainer: React.FC<ChartContainerProps> = ({
  title,
  subtitle,
  children,
  loading = false,
  error,
  action,
  className = ''
}) => (
  <Card className={className}>
    <div className="flex items-center justify-between mb-4">
      <div>
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        {subtitle && <p className="mt-1 text-sm text-gray-500">{subtitle}</p>}
      </div>
      {action && <div>{action}</div>}
    </div>
    
    {loading ? (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    ) : error ? (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="mt-2 text-sm text-gray-500">{error}</p>
        </div>
      </div>
    ) : (
      children
    )}
  </Card>
);

// Alert component for notifications
interface AlertProps {
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message?: string;
  onDismiss?: () => void;
  className?: string;
}

export const Alert: React.FC<AlertProps> = ({
  type,
  title,
  message,
  onDismiss,
  className = ''
}) => {
  const typeClasses = {
    info: 'bg-blue-50 border-blue-200 text-blue-800',
    success: 'bg-green-50 border-green-200 text-green-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    error: 'bg-red-50 border-red-200 text-red-800'
  };

  const iconClasses = {
    info: 'text-blue-400',
    success: 'text-green-400',
    warning: 'text-yellow-400',
    error: 'text-red-400'
  };

  return (
    <div className={`border rounded-md p-4 ${typeClasses[type]} ${className}`}>
      <div className="flex">
        <div className="flex-shrink-0">
          {type === 'success' && (
            <svg className={`h-5 w-5 ${iconClasses[type]}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          )}
          {type === 'warning' && (
            <svg className={`h-5 w-5 ${iconClasses[type]}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          )}
          {type === 'error' && (
            <svg className={`h-5 w-5 ${iconClasses[type]}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          )}
          {type === 'info' && (
            <svg className={`h-5 w-5 ${iconClasses[type]}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          )}
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium">{title}</h3>
          {message && <p className="mt-1 text-sm">{message}</p>}
        </div>
        {onDismiss && (
          <div className="ml-auto pl-3">
            <button
              onClick={onDismiss}
              className="inline-flex text-gray-400 hover:text-gray-600"
            >
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        )}
      </div>
    </div>
  );
};