import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';

interface FileUploadProps {
  onFilesSelected: (files: File[]) => void;
  maxFiles?: number;
  maxSize?: number;
  acceptedTypes?: string[];
  disabled?: boolean;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onFilesSelected,
  maxFiles = 5,
  maxSize = 10 * 1024 * 1024, // 10MB
  acceptedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
  disabled = false
}) => {
  const [uploadError, setUploadError] = useState<string | null>(null);

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setUploadError(null);

    if (rejectedFiles.length > 0) {
      const errors = rejectedFiles.map(file => {
        if (file.errors) {
          return file.errors.map((error: any) => {
            switch (error.code) {
              case 'file-too-large':
                return `File ${file.file.name} is too large. Maximum size is ${maxSize / 1024 / 1024}MB.`;
              case 'file-invalid-type':
                return `File ${file.file.name} has an invalid type. Accepted types: ${acceptedTypes.join(', ')}.`;
              case 'too-many-files':
                return `Too many files. Maximum ${maxFiles} files allowed.`;
              default:
                return `Error with file ${file.file.name}: ${error.message}`;
            }
          }).join(' ');
        }
        return `Error with file ${file.file.name}`;
      }).join(' ');
      
      setUploadError(errors);
      return;
    }

    if (acceptedFiles.length > 0) {
      onFilesSelected(acceptedFiles);
    }
  }, [onFilesSelected, maxFiles, maxSize, acceptedTypes]);

  const {
    getRootProps,
    getInputProps,
    isDragActive,
    isDragReject
  } = useDropzone({
    onDrop,
    maxFiles,
    maxSize,
    accept: acceptedTypes.reduce((acc, type) => {
      acc[type] = [];
      return acc;
    }, {} as Record<string, string[]>),
    disabled
  });

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-4">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
          disabled
            ? 'border-gray-200 bg-gray-50 cursor-not-allowed'
            : isDragActive
            ? isDragReject
              ? 'border-red-400 bg-red-50'
              : 'border-blue-400 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
      >
        <input {...getInputProps()} />
        
        <div className="space-y-2">
          <svg
            className={`mx-auto h-12 w-12 ${
              disabled ? 'text-gray-300' : 'text-gray-400'
            }`}
            stroke="currentColor"
            fill="none"
            viewBox="0 0 48 48"
          >
            <path
              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
              strokeWidth={2}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          
          {disabled ? (
            <p className="text-gray-500">File upload disabled</p>
          ) : isDragActive ? (
            isDragReject ? (
              <p className="text-red-600">Some files will be rejected</p>
            ) : (
              <p className="text-blue-600">Drop the files here...</p>
            )
          ) : (
            <div>
              <p className="text-gray-600">
                <span className="font-medium text-blue-600 hover:text-blue-500">
                  Click to upload
                </span>{' '}
                or drag and drop
              </p>
              <p className="text-xs text-gray-500">
                {acceptedTypes.includes('application/pdf') ? 'Images or PDF' : 'Images only'} up to {formatFileSize(maxSize)} each
              </p>
              <p className="text-xs text-gray-500">
                Maximum {maxFiles} files
              </p>
            </div>
          )}
        </div>
      </div>

      {uploadError && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <p className="text-red-600 text-sm">{uploadError}</p>
        </div>
      )}
    </div>
  );
};