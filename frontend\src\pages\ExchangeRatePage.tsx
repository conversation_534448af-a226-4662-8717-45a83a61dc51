
import React, { useState } from 'react';
import { useExchangeRate } from '../contexts/ExchangeRateContext';
import { useAuth } from '../contexts/AuthContext';

export const ExchangeRatePage: React.FC = () => {
  const { rates, loading, error, convertCurrency, fetchRates } = useExchangeRate();
  const { user, session } = useAuth();
  const [amount, setAmount] = useState(1);
  const [fromCurrency, setFromCurrency] = useState('USD');
  const [toCurrency, setToCurrency] = useState('KWD');
  const [conversionResult, setConversionResult] = useState<string | null>(null);

  // Manual rate override state
  const [showManualOverride, setShowManualOverride] = useState(false);
  const [manualRate, setManualRate] = useState('');
  const [manualFromCurrency, setManualFromCurrency] = useState('USD');
  const [manualToCurrency, setManualToCurrency] = useState('KWD');
  const [manualDate, setManualDate] = useState(new Date().toISOString().split('T')[0]);
  const [overrideLoading, setOverrideLoading] = useState(false);
  const [overrideMessage, setOverrideMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // Job control state
  const [jobLoading, setJobLoading] = useState(false);
  const [jobMessage, setJobMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const handleConversion = async () => {
    const result = await convertCurrency(amount, fromCurrency, toCurrency);
    if (result) {
      setConversionResult(`${result.amount} ${result.fromCurrency} = ${result.convertedAmount} ${result.toCurrency}`);
    }
  };

  const handleManualRateOverride = async () => {
    if (!session?.access_token) return;

    setOverrideLoading(true);
    setOverrideMessage(null);

    try {
      const response = await fetch('/api/exchange-rates/manual-update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          fromCurrency: manualFromCurrency,
          toCurrency: manualToCurrency,
          rate: parseFloat(manualRate),
          date: manualDate
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to update exchange rate');
      }

      setOverrideMessage({ type: 'success', text: 'Exchange rate updated successfully' });
      setShowManualOverride(false);
      setManualRate('');
      await fetchRates(); // Refresh the rates
    } catch (error) {
      setOverrideMessage({
        type: 'error',
        text: error instanceof Error ? error.message : 'Failed to update exchange rate'
      });
    } finally {
      setOverrideLoading(false);
    }
  };

  const handleFetchCurrentRates = async () => {
    if (!session?.access_token) return;

    setJobLoading(true);
    setJobMessage(null);

    try {
      const response = await fetch('/api/exchange-rates/fetch-current', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to fetch current rates');
      }

      setJobMessage({ type: 'success', text: 'Current exchange rates fetched successfully' });
      await fetchRates(); // Refresh the rates
    } catch (error) {
      setJobMessage({
        type: 'error',
        text: error instanceof Error ? error.message : 'Failed to fetch current rates'
      });
    } finally {
      setJobLoading(false);
    }
  };

  // Check if user is admin (you may need to adjust this based on your user role system)
  const isAdmin = user?.email?.includes('admin') || user?.user_metadata?.role === 'admin';

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Exchange Rates</h1>
        {isAdmin && (
          <div className="flex space-x-2">
            <button
              onClick={handleFetchCurrentRates}
              disabled={jobLoading}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md disabled:opacity-50"
            >
              {jobLoading ? 'Fetching...' : 'Fetch Current Rates'}
            </button>
            <button
              onClick={() => setShowManualOverride(true)}
              className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-md"
            >
              Manual Override
            </button>
          </div>
        )}
      </div>

      {/* Admin Messages */}
      {(overrideMessage || jobMessage) && (
        <div className={`p-4 rounded-md ${
          (overrideMessage?.type === 'success' || jobMessage?.type === 'success')
            ? 'bg-green-50 text-green-800'
            : 'bg-red-50 text-red-800'
        }`}>
          <p>{overrideMessage?.text || jobMessage?.text}</p>
        </div>
      )}

      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium mb-4">Currency Converter</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
          <div>
            <label className="block text-sm font-medium text-gray-700">Amount</label>
            <input
              type="number"
              value={amount}
              onChange={(e) => setAmount(Number(e.target.value))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">From</label>
            <select
              value={fromCurrency}
              onChange={(e) => setFromCurrency(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option>USD</option>
              <option>GBP</option>
              <option>KWD</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">To</label>
            <select
              value={toCurrency}
              onChange={(e) => setToCurrency(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option>USD</option>
              <option>GBP</option>
              <option>KWD</option>
            </select>
          </div>
          <button
            onClick={handleConversion}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
          >
            Convert
          </button>
        </div>
        {conversionResult && (
          <div className="mt-4 p-4 bg-blue-50 rounded-md">
            <p className="text-blue-800">{conversionResult}</p>
          </div>
        )}
      </div>

      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Exchange Rate History</h3>
        </div>
        <div className="border-t border-gray-200">
          {loading ? (
            <p className="p-4">Loading rates...</p>
          ) : error ? (
            <p className="p-4 text-red-600">{error}</p>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">From</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">To</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Rate</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {rates.map((rate) => (
                  <tr key={rate.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(rate.date).toLocaleDateString()}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{rate.from_currency}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{rate.to_currency}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{rate.rate}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>

      {/* Manual Rate Override Modal */}
      {showManualOverride && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Manual Rate Override</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">From Currency</label>
                  <select
                    value={manualFromCurrency}
                    onChange={(e) => setManualFromCurrency(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="USD">USD</option>
                    <option value="GBP">GBP</option>
                    <option value="KWD">KWD</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">To Currency</label>
                  <select
                    value={manualToCurrency}
                    onChange={(e) => setManualToCurrency(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="USD">USD</option>
                    <option value="GBP">GBP</option>
                    <option value="KWD">KWD</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Exchange Rate</label>
                  <input
                    type="number"
                    step="0.000001"
                    value={manualRate}
                    onChange={(e) => setManualRate(e.target.value)}
                    placeholder="Enter exchange rate"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Date</label>
                  <input
                    type="date"
                    value={manualDate}
                    onChange={(e) => setManualDate(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
              </div>
              <div className="flex justify-end space-x-2 mt-6">
                <button
                  onClick={() => {
                    setShowManualOverride(false);
                    setOverrideMessage(null);
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  Cancel
                </button>
                <button
                  onClick={handleManualRateOverride}
                  disabled={overrideLoading || !manualRate}
                  className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50"
                >
                  {overrideLoading ? 'Updating...' : 'Update Rate'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
