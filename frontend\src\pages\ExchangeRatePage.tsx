
import React, { useState } from 'react';
import { useExchangeRate } from '../contexts/ExchangeRateContext';

export const ExchangeRatePage: React.FC = () => {
  const { rates, loading, error, convertCurrency } = useExchangeRate();
  const [amount, setAmount] = useState(1);
  const [fromCurrency, setFromCurrency] = useState('USD');
  const [toCurrency, setToCurrency] = useState('KWD');
  const [conversionResult, setConversionResult] = useState<string | null>(null);

  const handleConversion = async () => {
    const result = await convertCurrency(amount, fromCurrency, toCurrency);
    if (result) {
      setConversionResult(`${result.amount} ${result.fromCurrency} = ${result.convertedAmount} ${result.toCurrency}`);
    }
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Exchange Rates</h1>

      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium mb-4">Currency Converter</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
          <div>
            <label className="block text-sm font-medium text-gray-700">Amount</label>
            <input
              type="number"
              value={amount}
              onChange={(e) => setAmount(Number(e.target.value))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">From</label>
            <select
              value={fromCurrency}
              onChange={(e) => setFromCurrency(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option>USD</option>
              <option>GBP</option>
              <option>KWD</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">To</label>
            <select
              value={toCurrency}
              onChange={(e) => setToCurrency(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option>USD</option>
              <option>GBP</option>
              <option>KWD</option>
            </select>
          </div>
          <button
            onClick={handleConversion}
            className="bg-blue-600 text-white px-4 py-2 rounded-md"
          >
            Convert
          </button>
        </div>
        {conversionResult && (
          <div className="mt-4 p-4 bg-blue-50 rounded-md">
            <p className="text-blue-800">{conversionResult}</p>
          </div>
        )}
      </div>

      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Exchange Rate History</h3>
        </div>
        <div className="border-t border-gray-200">
          {loading ? (
            <p className="p-4">Loading rates...</p>
          ) : error ? (
            <p className="p-4 text-red-600">{error}</p>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">From</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">To</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Rate</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {rates.map((rate) => (
                  <tr key={rate.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(rate.date).toLocaleDateString()}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{rate.from_currency}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{rate.to_currency}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{rate.rate}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
};
