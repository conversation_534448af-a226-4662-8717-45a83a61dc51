import { Router } from 'express';
import { z } from 'zod';
import multer from 'multer';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import { PaymentService } from '../services/paymentService';
import { supabaseAdmin } from '../config/supabase';
import { config } from '../config/config';

const router = Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: parseInt(config.MAX_FILE_SIZE), // 10MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = config.ALLOWED_FILE_TYPES.split(',');
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type'));
    }
  }
});

// Validation schemas
const paymentSchema = z.object({
  subscription_id: z.string().uuid('Invalid subscription ID'),
  amount: z.number().positive('Amount must be positive').max(999999.99, 'Amount too large'),
  currency: z.enum(['USD', 'GBP', 'KWD']),
  payment_date: z.string().datetime('Invalid date format'),
  payment_method: z.string().min(1, 'Payment method is required').max(100, 'Payment method too long'),
  reference_number: z.string().max(100, 'Reference number too long').optional(),
  notes: z.string().max(500, 'Notes too long').optional(),
  status: z.enum(['pending', 'completed', 'failed', 'refunded']).default('completed')
});

const updatePaymentSchema = paymentSchema.partial();

const filtersSchema = z.object({
  subscription_id: z.string().uuid().optional(),
  business_id: z.string().uuid().optional(),
  status: z.enum(['pending', 'completed', 'failed', 'refunded']).optional(),
  currency: z.enum(['USD', 'GBP', 'KWD']).optional(),
  payment_method: z.string().optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  limit: z.string().transform(val => parseInt(val)).pipe(z.number().min(1).max(100)).optional(),
  offset: z.string().transform(val => parseInt(val)).pipe(z.number().min(0)).optional(),
  q: z.string().min(1).optional()
});

// GET /api/payments
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const filters = filtersSchema.parse(req.query);
  
  const { q, limit = 50, offset = 0, ...paymentFilters } = filters;

  let result;
  if (q) {
    const payments = await PaymentService.searchPayments(userId, q, paymentFilters);
    result = { payments, total: payments.length };
  } else {
    result = await PaymentService.getUserPayments(userId, paymentFilters, limit, offset);
  }

  res.json({
    success: true,
    message: 'Payments retrieved successfully',
    data: result.payments,
    pagination: {
      total: result.total,
      limit,
      offset,
      hasMore: offset + limit < result.total
    }
  });
}));

// GET /api/payments/methods
router.get('/methods', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const methods = await PaymentService.getPaymentMethods(userId);

  res.json({
    success: true,
    message: 'Payment methods retrieved successfully',
    data: methods
  });
}));

// GET /api/payments/:id
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const userId = req.user!.id;
  
  const payment = await PaymentService.getPaymentById(id, userId);
  
  res.json({
    success: true,
    message: 'Payment retrieved successfully',
    data: payment
  });
}));

// GET /api/payments/:id/attachments
router.get('/:id/attachments', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const userId = req.user!.id;
  
  const attachments = await PaymentService.getPaymentAttachments(id, userId);
  
  res.json({
    success: true,
    message: 'Payment attachments retrieved successfully',
    data: attachments
  });
}));

// POST /api/payments
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const paymentData = paymentSchema.parse(req.body);
  
  const payment = await PaymentService.createPayment(userId, paymentData);
  
  res.status(201).json({
    success: true,
    message: 'Payment created successfully',
    data: payment
  });
}));

// POST /api/payments/:id/attachments
router.post('/:id/attachments', upload.array('files', 5), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const files = req.files as Express.Multer.File[];

  if (!files || files.length === 0) {
    throw new Error('No files uploaded');
  }

  // Verify payment belongs to user
  await PaymentService.getPaymentById(id, userId);

  const uploadedAttachments = [];

  for (const file of files) {
    // Generate unique file path
    const timestamp = Date.now();
    const fileName = `${timestamp}-${file.originalname}`;
    const filePath = `payments/${id}/${fileName}`;

    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabaseAdmin.storage
      .from('payment-receipts')
      .upload(filePath, file.buffer, {
        contentType: file.mimetype,
        upsert: false
      });

    if (uploadError) {
      throw new Error(`Failed to upload file: ${uploadError.message}`);
    }

    // Save attachment record
    const attachment = await PaymentService.addPaymentAttachment(
      id,
      userId,
      file.originalname,
      filePath,
      file.size,
      file.mimetype
    );

    uploadedAttachments.push(attachment);
  }

  res.status(201).json({
    success: true,
    message: 'Files uploaded successfully',
    data: uploadedAttachments
  });
}));

// PUT /api/payments/:id
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const updates = updatePaymentSchema.parse(req.body);
  
  const payment = await PaymentService.updatePayment(id, userId, updates);
  
  res.json({
    success: true,
    message: 'Payment updated successfully',
    data: payment
  });
}));

// DELETE /api/payments/:id
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const userId = req.user!.id;
  
  await PaymentService.deletePayment(id, userId);
  
  res.json({
    success: true,
    message: 'Payment deleted successfully'
  });
}));

// GET /api/payments/business/:businessId/stats
router.get('/business/:businessId/stats', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { businessId } = req.params;
  const userId = req.user!.id;
  
  const stats = await PaymentService.getBusinessPaymentStats(businessId, userId);
  
  res.json({
    success: true,
    message: 'Business payment statistics retrieved successfully',
    data: stats
  });
}));

export { router as paymentRoutes };