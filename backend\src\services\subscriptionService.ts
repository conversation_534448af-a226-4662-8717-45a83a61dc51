import { supabaseAdmin } from '../config/supabase';
import { createError } from '../middleware/errorHandler';

export interface Subscription {
  id: string;
  business_id: string;
  service_name: string;
  provider: string;
  amount: number;
  currency: 'USD' | 'GBP' | 'KWD';
  billing_frequency: 'monthly' | 'quarterly' | 'yearly';
  category: string | null;
  status: 'active' | 'inactive' | 'cancelled';
  next_payment_date: string;
  created_at: string;
  updated_at: string;
}

export interface CreateSubscriptionData {
  business_id: string;
  service_name: string;
  provider: string;
  amount: number;
  currency: 'USD' | 'GBP' | 'KWD';
  billing_frequency: 'monthly' | 'quarterly' | 'yearly';
  category?: string;
  status?: 'active' | 'inactive' | 'cancelled';
  next_payment_date: string;
}

export interface UpdateSubscriptionData extends Partial<CreateSubscriptionData> {}

export interface SubscriptionFilters {
  business_id?: string;
  status?: 'active' | 'inactive' | 'cancelled';
  category?: string;
  currency?: 'USD' | 'GBP' | 'KWD';
  provider?: string;
}

export class SubscriptionService {
  /**
   * Get subscriptions for a user with optional filters
   */
  static async getUserSubscriptions(
    userId: string,
    filters: SubscriptionFilters = {},
    limit: number = 50,
    offset: number = 0
  ): Promise<{ subscriptions: Subscription[]; total: number }> {
    try {
      let query = supabaseAdmin
        .from('subscriptions')
        .select(`
          *,
          businesses!inner(user_id)
        `, { count: 'exact' })
        .eq('businesses.user_id', userId);

      // Apply filters
      if (filters.business_id) {
        query = query.eq('business_id', filters.business_id);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.category) {
        query = query.eq('category', filters.category);
      }
      if (filters.currency) {
        query = query.eq('currency', filters.currency);
      }
      if (filters.provider) {
        query = query.ilike('provider', `%${filters.provider}%`);
      }

      // Apply pagination and ordering
      query = query
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      if (error) {
        throw createError(`Failed to fetch subscriptions: ${error.message}`, 500);
      }

      return {
        subscriptions: data || [],
        total: count || 0
      };
    } catch (error) {
      console.error('Failed to get user subscriptions:', error);
      throw error;
    }
  }

  /**
   * Get a specific subscription by ID
   */
  static async getSubscriptionById(subscriptionId: string, userId: string): Promise<Subscription> {
    try {
      const { data, error } = await supabaseAdmin
        .from('subscriptions')
        .select(`
          *,
          businesses!inner(user_id)
        `)
        .eq('id', subscriptionId)
        .eq('businesses.user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          throw createError('Subscription not found', 404);
        }
        throw createError(`Failed to fetch subscription: ${error.message}`, 500);
      }

      return data;
    } catch (error) {
      console.error('Failed to get subscription by ID:', error);
      throw error;
    }
  }

  /**
   * Create a new subscription
   */
  static async createSubscription(userId: string, subscriptionData: CreateSubscriptionData): Promise<Subscription> {
    try {
      // Verify business belongs to user
      const { data: business, error: businessError } = await supabaseAdmin
        .from('businesses')
        .select('id')
        .eq('id', subscriptionData.business_id)
        .eq('user_id', userId)
        .single();

      if (businessError || !business) {
        throw createError('Business not found or access denied', 404);
      }

      const { data, error } = await supabaseAdmin
        .from('subscriptions')
        .insert({
          business_id: subscriptionData.business_id,
          service_name: subscriptionData.service_name,
          provider: subscriptionData.provider,
          amount: subscriptionData.amount,
          currency: subscriptionData.currency,
          billing_frequency: subscriptionData.billing_frequency,
          category: subscriptionData.category || null,
          status: subscriptionData.status || 'active',
          next_payment_date: subscriptionData.next_payment_date
        })
        .select()
        .single();

      if (error) {
        throw createError(`Failed to create subscription: ${error.message}`, 400);
      }

      return data;
    } catch (error) {
      console.error('Failed to create subscription:', error);
      throw error;
    }
  }

  /**
   * Update an existing subscription
   */
  static async updateSubscription(
    subscriptionId: string,
    userId: string,
    updates: UpdateSubscriptionData
  ): Promise<Subscription> {
    try {
      // First verify the subscription belongs to the user
      await this.getSubscriptionById(subscriptionId, userId);

      // If business_id is being updated, verify the new business belongs to user
      if (updates.business_id) {
        const { data: business, error: businessError } = await supabaseAdmin
          .from('businesses')
          .select('id')
          .eq('id', updates.business_id)
          .eq('user_id', userId)
          .single();

        if (businessError || !business) {
          throw createError('Business not found or access denied', 404);
        }
      }

      const { data, error } = await supabaseAdmin
        .from('subscriptions')
        .update(updates)
        .eq('id', subscriptionId)
        .select(`
          *,
          businesses!inner(user_id)
        `)
        .eq('businesses.user_id', userId)
        .single();

      if (error) {
        throw createError(`Failed to update subscription: ${error.message}`, 400);
      }

      return data;
    } catch (error) {
      console.error('Failed to update subscription:', error);
      throw error;
    }
  }

  /**
   * Delete a subscription
   */
  static async deleteSubscription(subscriptionId: string, userId: string): Promise<void> {
    try {
      // First verify the subscription belongs to the user
      await this.getSubscriptionById(subscriptionId, userId);

      // Check if subscription has associated payments
      const { data: payments, error: paymentsError } = await supabaseAdmin
        .from('payments')
        .select('id')
        .eq('subscription_id', subscriptionId)
        .limit(1);

      if (paymentsError) {
        throw createError(`Failed to check payments: ${paymentsError.message}`, 500);
      }

      if (payments && payments.length > 0) {
        throw createError(
          'Cannot delete subscription with existing payments. Please delete all payments first.',
          400
        );
      }

      // Delete the subscription
      const { error } = await supabaseAdmin
        .from('subscriptions')
        .delete()
        .eq('id', subscriptionId);

      if (error) {
        throw createError(`Failed to delete subscription: ${error.message}`, 500);
      }
    } catch (error) {
      console.error('Failed to delete subscription:', error);
      throw error;
    }
  }

  /**
   * Get subscription categories for a user
   */
  static async getSubscriptionCategories(userId: string): Promise<string[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('subscriptions')
        .select(`
          category,
          businesses!inner(user_id)
        `)
        .eq('businesses.user_id', userId)
        .not('category', 'is', null);

      if (error) {
        throw createError(`Failed to fetch categories: ${error.message}`, 500);
      }

      // Extract unique categories
      const categories = [...new Set(data?.map(item => item.category).filter(Boolean))] as string[];
      return categories.sort();
    } catch (error) {
      console.error('Failed to get subscription categories:', error);
      throw error;
    }
  }

  /**
   * Get upcoming payments for subscriptions
   */
  static async getUpcomingPayments(
    userId: string,
    days: number = 30
  ): Promise<Subscription[]> {
    try {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + days);

      const { data, error } = await supabaseAdmin
        .from('subscriptions')
        .select(`
          *,
          businesses!inner(user_id)
        `)
        .eq('businesses.user_id', userId)
        .eq('status', 'active')
        .lte('next_payment_date', futureDate.toISOString())
        .order('next_payment_date', { ascending: true });

      if (error) {
        throw createError(`Failed to fetch upcoming payments: ${error.message}`, 500);
      }

      return data || [];
    } catch (error) {
      console.error('Failed to get upcoming payments:', error);
      throw error;
    }
  }

  /**
   * Calculate next payment date based on billing frequency
   */
  static calculateNextPaymentDate(currentDate: Date, frequency: 'monthly' | 'quarterly' | 'yearly'): Date {
    const nextDate = new Date(currentDate);
    
    switch (frequency) {
      case 'monthly':
        nextDate.setMonth(nextDate.getMonth() + 1);
        break;
      case 'quarterly':
        nextDate.setMonth(nextDate.getMonth() + 3);
        break;
      case 'yearly':
        nextDate.setFullYear(nextDate.getFullYear() + 1);
        break;
    }
    
    return nextDate;
  }

  /**
   * Get subscription statistics for a business
   */
  static async getBusinessSubscriptionStats(businessId: string, userId: string) {
    try {
      // Verify business belongs to user
      const { data: business, error: businessError } = await supabaseAdmin
        .from('businesses')
        .select('id')
        .eq('id', businessId)
        .eq('user_id', userId)
        .single();

      if (businessError || !business) {
        throw createError('Business not found or access denied', 404);
      }

      // Get subscription counts by status
      const { data: statusCounts, error: statusError } = await supabaseAdmin
        .from('subscriptions')
        .select('status')
        .eq('business_id', businessId);

      if (statusError) {
        throw createError(`Failed to get status counts: ${statusError.message}`, 500);
      }

      const stats = {
        total: statusCounts?.length || 0,
        active: statusCounts?.filter(s => s.status === 'active').length || 0,
        inactive: statusCounts?.filter(s => s.status === 'inactive').length || 0,
        cancelled: statusCounts?.filter(s => s.status === 'cancelled').length || 0
      };

      // Get total monthly cost
      const { data: activeSubscriptions, error: activeError } = await supabaseAdmin
        .from('subscriptions')
        .select('amount, currency, billing_frequency')
        .eq('business_id', businessId)
        .eq('status', 'active');

      if (activeError) {
        throw createError(`Failed to get active subscriptions: ${activeError.message}`, 500);
      }

      // Calculate monthly equivalent costs
      const monthlyCosts = (activeSubscriptions || []).reduce((acc, sub) => {
        let monthlyAmount = sub.amount;
        
        switch (sub.billing_frequency) {
          case 'quarterly':
            monthlyAmount = sub.amount / 3;
            break;
          case 'yearly':
            monthlyAmount = sub.amount / 12;
            break;
        }

        acc[sub.currency] = (acc[sub.currency] || 0) + monthlyAmount;
        return acc;
      }, {} as Record<string, number>);

      return {
        ...stats,
        monthlyCosts
      };
    } catch (error) {
      console.error('Failed to get business subscription stats:', error);
      throw error;
    }
  }

  /**
   * Search subscriptions by service name or provider
   */
  static async searchSubscriptions(
    userId: string,
    searchTerm: string,
    filters: SubscriptionFilters = {}
  ): Promise<Subscription[]> {
    try {
      let query = supabaseAdmin
        .from('subscriptions')
        .select(`
          *,
          businesses!inner(user_id)
        `)
        .eq('businesses.user_id', userId)
        .or(`service_name.ilike.%${searchTerm}%,provider.ilike.%${searchTerm}%`);

      // Apply additional filters
      if (filters.business_id) {
        query = query.eq('business_id', filters.business_id);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.category) {
        query = query.eq('category', filters.category);
      }

      query = query.order('created_at', { ascending: false });

      const { data, error } = await query;

      if (error) {
        throw createError(`Failed to search subscriptions: ${error.message}`, 500);
      }

      return data || [];
    } catch (error) {
      console.error('Failed to search subscriptions:', error);
      throw error;
    }
  }
}