
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSubscription, Subscription } from '../contexts/SubscriptionContext';
import { usePayment, Payment } from '../contexts/PaymentContext';
import { PaymentTable } from '../components/payment/PaymentTable';

export const SubscriptionDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { subscriptions, getSubscriptionById } = useSubscription();
  const { payments, fetchPayments } = usePayment();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      const loadSubscription = async () => {
        setLoading(true);
        const sub = await getSubscriptionById(id);
        if (sub) {
          setSubscription(sub);
          fetchPayments({ subscription_id: id });
        } else {
          navigate('/subscriptions');
        }
        setLoading(false);
      };
      loadSubscription();
    }
  }, [id, getSubscriptionById, fetchPayments, navigate]);

  if (loading) {
    return <p>Loading...</p>;
  }

  if (!subscription) {
    return <p>Subscription not found</p>;
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">{subscription.service_name}</h1>
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium mb-4">Subscription Details</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div><span className="font-medium">Provider:</span> {subscription.provider}</div>
          <div><span className="font-medium">Amount:</span> {subscription.amount} {subscription.currency}</div>
          <div><span className="font-medium">Billing Frequency:</span> {subscription.billing_frequency}</div>
          <div><span className="font-medium">Category:</span> {subscription.category}</div>
          <div><span className="font-medium">Status:</span> {subscription.status}</div>
          <div><span className="font-medium">Next Payment:</span> {new Date(subscription.next_payment_date).toLocaleDateString()}</div>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Payment History</h3>
        </div>
        <div className="border-t border-gray-200">
          <PaymentTable payments={payments} />
        </div>
      </div>
    </div>
  );
};
