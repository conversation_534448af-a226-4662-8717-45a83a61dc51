import { supabaseAdmin } from '../config/supabase';
import { createError } from '../middleware/errorHandler';

export interface Payment {
  id: string;
  subscription_id: string;
  amount: number;
  currency: 'USD' | 'GBP' | 'KWD';
  payment_date: string;
  payment_method: string;
  reference_number: string | null;
  notes: string | null;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  created_at: string;
  updated_at: string;
}

export interface PaymentAttachment {
  id: string;
  payment_id: string;
  file_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  created_at: string;
}

export interface CreatePaymentData {
  subscription_id: string;
  amount: number;
  currency: 'USD' | 'GBP' | 'KWD';
  payment_date: string;
  payment_method: string;
  reference_number?: string;
  notes?: string;
  status?: 'pending' | 'completed' | 'failed' | 'refunded';
}

export interface UpdatePaymentData extends Partial<CreatePaymentData> {}

export interface PaymentFilters {
  subscription_id?: string;
  business_id?: string;
  status?: 'pending' | 'completed' | 'failed' | 'refunded';
  currency?: 'USD' | 'GBP' | 'KWD';
  payment_method?: string;
  date_from?: string;
  date_to?: string;
}

export class PaymentService {
  /**
   * Get payments for a user with optional filters
   */
  static async getUserPayments(
    userId: string,
    filters: PaymentFilters = {},
    limit: number = 50,
    offset: number = 0
  ): Promise<{ payments: Payment[]; total: number }> {
    try {
      let query = supabaseAdmin
        .from('payments')
        .select(`
          *,
          subscriptions!inner(
            id,
            service_name,
            provider,
            businesses!inner(user_id)
          )
        `, { count: 'exact' })
        .eq('subscriptions.businesses.user_id', userId);

      // Apply filters
      if (filters.subscription_id) {
        query = query.eq('subscription_id', filters.subscription_id);
      }
      if (filters.business_id) {
        query = query.eq('subscriptions.business_id', filters.business_id);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.currency) {
        query = query.eq('currency', filters.currency);
      }
      if (filters.payment_method) {
        query = query.ilike('payment_method', `%${filters.payment_method}%`);
      }
      if (filters.date_from) {
        query = query.gte('payment_date', filters.date_from);
      }
      if (filters.date_to) {
        query = query.lte('payment_date', filters.date_to);
      }

      // Apply pagination and ordering
      query = query
        .order('payment_date', { ascending: false })
        .range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      if (error) {
        throw createError(`Failed to fetch payments: ${error.message}`, 500);
      }

      return {
        payments: data || [],
        total: count || 0
      };
    } catch (error) {
      console.error('Failed to get user payments:', error);
      throw error;
    }
  }

  /**
   * Get a specific payment by ID
   */
  static async getPaymentById(paymentId: string, userId: string): Promise<Payment> {
    try {
      const { data, error } = await supabaseAdmin
        .from('payments')
        .select(`
          *,
          subscriptions!inner(
            id,
            service_name,
            provider,
            businesses!inner(user_id)
          )
        `)
        .eq('id', paymentId)
        .eq('subscriptions.businesses.user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          throw createError('Payment not found', 404);
        }
        throw createError(`Failed to fetch payment: ${error.message}`, 500);
      }

      return data;
    } catch (error) {
      console.error('Failed to get payment by ID:', error);
      throw error;
    }
  }

  /**
   * Create a new payment
   */
  static async createPayment(userId: string, paymentData: CreatePaymentData): Promise<Payment> {
    try {
      // Verify subscription belongs to user
      const { data: subscription, error: subscriptionError } = await supabaseAdmin
        .from('subscriptions')
        .select(`
          id,
          businesses!inner(user_id)
        `)
        .eq('id', paymentData.subscription_id)
        .eq('businesses.user_id', userId)
        .single();

      if (subscriptionError || !subscription) {
        throw createError('Subscription not found or access denied', 404);
      }

      const { data, error } = await supabaseAdmin
        .from('payments')
        .insert({
          subscription_id: paymentData.subscription_id,
          amount: paymentData.amount,
          currency: paymentData.currency,
          payment_date: paymentData.payment_date,
          payment_method: paymentData.payment_method,
          reference_number: paymentData.reference_number || null,
          notes: paymentData.notes || null,
          status: paymentData.status || 'completed'
        })
        .select()
        .single();

      if (error) {
        throw createError(`Failed to create payment: ${error.message}`, 400);
      }

      return data;
    } catch (error) {
      console.error('Failed to create payment:', error);
      throw error;
    }
  }

  /**
   * Update an existing payment
   */
  static async updatePayment(
    paymentId: string,
    userId: string,
    updates: UpdatePaymentData
  ): Promise<Payment> {
    try {
      // First verify the payment belongs to the user
      await this.getPaymentById(paymentId, userId);

      // If subscription_id is being updated, verify the new subscription belongs to user
      if (updates.subscription_id) {
        const { data: subscription, error: subscriptionError } = await supabaseAdmin
          .from('subscriptions')
          .select(`
            id,
            businesses!inner(user_id)
          `)
          .eq('id', updates.subscription_id)
          .eq('businesses.user_id', userId)
          .single();

        if (subscriptionError || !subscription) {
          throw createError('Subscription not found or access denied', 404);
        }
      }

      const { data, error } = await supabaseAdmin
        .from('payments')
        .update(updates)
        .eq('id', paymentId)
        .select(`
          *,
          subscriptions!inner(
            id,
            service_name,
            provider,
            businesses!inner(user_id)
          )
        `)
        .eq('subscriptions.businesses.user_id', userId)
        .single();

      if (error) {
        throw createError(`Failed to update payment: ${error.message}`, 400);
      }

      return data;
    } catch (error) {
      console.error('Failed to update payment:', error);
      throw error;
    }
  }

  /**
   * Delete a payment
   */
  static async deletePayment(paymentId: string, userId: string): Promise<void> {
    try {
      // First verify the payment belongs to the user
      await this.getPaymentById(paymentId, userId);

      // Check if payment is linked to reimbursements
      const { data: reimbursementLinks, error: reimbursementError } = await supabaseAdmin
        .from('reimbursement_payments')
        .select('id')
        .eq('payment_id', paymentId)
        .limit(1);

      if (reimbursementError) {
        throw createError(`Failed to check reimbursement links: ${reimbursementError.message}`, 500);
      }

      if (reimbursementLinks && reimbursementLinks.length > 0) {
        throw createError(
          'Cannot delete payment that is linked to reimbursements. Please remove from reimbursements first.',
          400
        );
      }

      // Delete associated attachments first
      await this.deletePaymentAttachments(paymentId);

      // Delete the payment
      const { error } = await supabaseAdmin
        .from('payments')
        .delete()
        .eq('id', paymentId);

      if (error) {
        throw createError(`Failed to delete payment: ${error.message}`, 500);
      }
    } catch (error) {
      console.error('Failed to delete payment:', error);
      throw error;
    }
  }

  /**
   * Get payment attachments
   */
  static async getPaymentAttachments(paymentId: string, userId: string): Promise<PaymentAttachment[]> {
    try {
      // Verify payment belongs to user
      await this.getPaymentById(paymentId, userId);

      const { data, error } = await supabaseAdmin
        .from('payment_attachments')
        .select('*')
        .eq('payment_id', paymentId)
        .order('created_at', { ascending: false });

      if (error) {
        throw createError(`Failed to fetch payment attachments: ${error.message}`, 500);
      }

      return data || [];
    } catch (error) {
      console.error('Failed to get payment attachments:', error);
      throw error;
    }
  }

  /**
   * Add payment attachment
   */
  static async addPaymentAttachment(
    paymentId: string,
    userId: string,
    fileName: string,
    filePath: string,
    fileSize: number,
    mimeType: string
  ): Promise<PaymentAttachment> {
    try {
      // Verify payment belongs to user
      await this.getPaymentById(paymentId, userId);

      const { data, error } = await supabaseAdmin
        .from('payment_attachments')
        .insert({
          payment_id: paymentId,
          file_name: fileName,
          file_path: filePath,
          file_size: fileSize,
          mime_type: mimeType
        })
        .select()
        .single();

      if (error) {
        throw createError(`Failed to add payment attachment: ${error.message}`, 400);
      }

      return data;
    } catch (error) {
      console.error('Failed to add payment attachment:', error);
      throw error;
    }
  }

  /**
   * Delete payment attachments
   */
  static async deletePaymentAttachments(paymentId: string): Promise<void> {
    try {
      // Get all attachments for the payment
      const { data: attachments, error: fetchError } = await supabaseAdmin
        .from('payment_attachments')
        .select('file_path')
        .eq('payment_id', paymentId);

      if (fetchError) {
        throw createError(`Failed to fetch attachments: ${fetchError.message}`, 500);
      }

      // Delete files from storage
      if (attachments && attachments.length > 0) {
        const filePaths = attachments.map(att => att.file_path);
        const { error: storageError } = await supabaseAdmin.storage
          .from('payment-receipts')
          .remove(filePaths);

        if (storageError) {
          console.error('Failed to delete files from storage:', storageError);
        }
      }

      // Delete attachment records
      const { error } = await supabaseAdmin
        .from('payment_attachments')
        .delete()
        .eq('payment_id', paymentId);

      if (error) {
        throw createError(`Failed to delete payment attachments: ${error.message}`, 500);
      }
    } catch (error) {
      console.error('Failed to delete payment attachments:', error);
      throw error;
    }
  }

  /**
   * Get payment statistics for a business
   */
  static async getBusinessPaymentStats(businessId: string, userId: string) {
    try {
      // Verify business belongs to user
      const { data: business, error: businessError } = await supabaseAdmin
        .from('businesses')
        .select('id')
        .eq('id', businessId)
        .eq('user_id', userId)
        .single();

      if (businessError || !business) {
        throw createError('Business not found or access denied', 404);
      }

      // Get payment counts by status
      const { data: payments, error: paymentsError } = await supabaseAdmin
        .from('payments')
        .select(`
          status,
          amount,
          currency,
          subscriptions!inner(business_id)
        `)
        .eq('subscriptions.business_id', businessId);

      if (paymentsError) {
        throw createError(`Failed to get payments: ${paymentsError.message}`, 500);
      }

      const stats = {
        total: payments?.length || 0,
        completed: payments?.filter(p => p.status === 'completed').length || 0,
        pending: payments?.filter(p => p.status === 'pending').length || 0,
        failed: payments?.filter(p => p.status === 'failed').length || 0,
        refunded: payments?.filter(p => p.status === 'refunded').length || 0
      };

      // Calculate total amounts by currency
      const totalAmounts = (payments || []).reduce((acc, payment) => {
        if (payment.status === 'completed') {
          acc[payment.currency] = (acc[payment.currency] || 0) + payment.amount;
        }
        return acc;
      }, {} as Record<string, number>);

      return {
        ...stats,
        totalAmounts
      };
    } catch (error) {
      console.error('Failed to get business payment stats:', error);
      throw error;
    }
  }

  /**
   * Search payments by reference number or notes
   */
  static async searchPayments(
    userId: string,
    searchTerm: string,
    filters: PaymentFilters = {}
  ): Promise<Payment[]> {
    try {
      let query = supabaseAdmin
        .from('payments')
        .select(`
          *,
          subscriptions!inner(
            id,
            service_name,
            provider,
            businesses!inner(user_id)
          )
        `)
        .eq('subscriptions.businesses.user_id', userId)
        .or(`reference_number.ilike.%${searchTerm}%,notes.ilike.%${searchTerm}%`);

      // Apply additional filters
      if (filters.business_id) {
        query = query.eq('subscriptions.business_id', filters.business_id);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.currency) {
        query = query.eq('currency', filters.currency);
      }

      query = query.order('payment_date', { ascending: false });

      const { data, error } = await query;

      if (error) {
        throw createError(`Failed to search payments: ${error.message}`, 500);
      }

      return data || [];
    } catch (error) {
      console.error('Failed to search payments:', error);
      throw error;
    }
  }

  /**
   * Get payment methods used by user
   */
  static async getPaymentMethods(userId: string): Promise<string[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('payments')
        .select(`
          payment_method,
          subscriptions!inner(
            businesses!inner(user_id)
          )
        `)
        .eq('subscriptions.businesses.user_id', userId)
        .not('payment_method', 'is', null);

      if (error) {
        throw createError(`Failed to fetch payment methods: ${error.message}`, 500);
      }

      // Extract unique payment methods
      const methods = [...new Set(data?.map(item => item.payment_method).filter(Boolean))] as string[];
      return methods.sort();
    } catch (error) {
      console.error('Failed to get payment methods:', error);
      throw error;
    }
  }
}