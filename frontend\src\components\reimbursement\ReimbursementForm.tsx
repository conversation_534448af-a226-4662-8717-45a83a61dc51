import React, { useState, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Reimbursement, CreateReimbursementData } from '../../contexts/ReimbursementContext';
import { usePayment } from '../../contexts/PaymentContext';
import { useBusiness } from '../../contexts/BusinessContext';

const reimbursementSchema = z.object({
  business_id: z.string().min(1, 'Business is required'),
  payment_ids: z.array(z.string()).min(1, 'At least one payment must be selected'),
  amount: z.number().positive('Amount must be positive').optional(),
  description: z.string().optional(),
  expected_date: z.string().optional()
});

type ReimbursementFormData = z.infer<typeof reimbursementSchema>;

interface ReimbursementFormProps {
  reimbursement?: Reimbursement;
  onSubmit: (data: CreateReimbursementData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  preselectedPaymentIds?: string[];
}

export const ReimbursementForm: React.FC<ReimbursementFormProps> = ({
  reimbursement,
  onSubmit,
  onCancel,
  isLoading = false,
  preselectedPaymentIds = []
}) => {
  const { payments, fetchPayments } = usePayment();
  const { businesses, currentBusiness } = useBusiness();
  const [error, setError] = useState<string | null>(null);
  const [selectedPayments, setSelectedPayments] = useState<Set<string>>(
    new Set(preselectedPaymentIds)
  );
  const [customAmount, setCustomAmount] = useState<number | null>(null);
  const [useCustomAmount, setUseCustomAmount] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<ReimbursementFormData>({
    resolver: zodResolver(reimbursementSchema),
    defaultValues: {
      business_id: reimbursement?.business_id || currentBusiness?.id || '',
      payment_ids: preselectedPaymentIds,
      description: reimbursement?.description || '',
      amount: reimbursement?.amount,
      expected_date: reimbursement?.expected_date 
        ? new Date(reimbursement.expected_date).toISOString().split('T')[0]
        : ''
    }
  });

  const selectedBusinessId = watch('business_id');

  // Filter payments by selected business
  const businessPayments = useMemo(() => {
    if (!selectedBusinessId) return [];
    return payments.filter(payment => {
      // We would need to include business_id in payment data or filter through subscriptions
      // For now, we'll need to fetch payments with business filter
      return payment.status === 'completed'; // Only show completed payments
    });
  }, [payments, selectedBusinessId]);

  // Calculate total amount of selected payments
  const selectedPaymentData = useMemo(() => {
    return businessPayments.filter(payment => selectedPayments.has(payment.id));
  }, [businessPayments, selectedPayments]);

  const totalAmount = useMemo(() => {
    return selectedPaymentData.reduce((sum, payment) => sum + payment.amount, 0);
  }, [selectedPaymentData]);

  // Calculate expected date based on business timeline
  const calculateExpectedDate = (businessId: string): string => {
    const business = businesses.find(b => b.id === businessId);
    if (!business) return '';
    
    const requestDate = new Date();
    const expectedDate = new Date(requestDate);
    expectedDate.setDate(expectedDate.getDate() + (business.reimbursement_timeline || 30));
    
    return expectedDate.toISOString().split('T')[0];
  };

  useEffect(() => {
    if (selectedBusinessId) {
      // Fetch payments for the selected business
      fetchPayments({ business_id: selectedBusinessId, status: 'completed' });
      
      // Update expected date when business changes
      if (!reimbursement) {
        const expectedDate = calculateExpectedDate(selectedBusinessId);
        setValue('expected_date', expectedDate);
      }
    }
  }, [selectedBusinessId, fetchPayments, setValue, businesses, reimbursement]);

  useEffect(() => {
    // Update payment_ids when selectedPayments changes
    setValue('payment_ids', Array.from(selectedPayments));
    
    // Update amount when payments change
    if (!useCustomAmount && totalAmount > 0) {
      setValue('amount', totalAmount);
    }
  }, [selectedPayments, setValue, totalAmount, useCustomAmount]);

  const handlePaymentSelection = (paymentId: string, checked: boolean) => {
    const newSelected = new Set(selectedPayments);
    if (checked) {
      newSelected.add(paymentId);
    } else {
      newSelected.delete(paymentId);
    }
    setSelectedPayments(newSelected);
  };

  const handleSelectAllPayments = () => {
    if (selectedPayments.size === businessPayments.length) {
      setSelectedPayments(new Set());
    } else {
      setSelectedPayments(new Set(businessPayments.map(p => p.id)));
    }
  };

  const handleCustomAmountToggle = () => {
    setUseCustomAmount(!useCustomAmount);
    if (!useCustomAmount) {
      setCustomAmount(totalAmount);
      setValue('amount', totalAmount);
    } else {
      setValue('amount', totalAmount);
    }
  };

  const handleFormSubmit = async (data: ReimbursementFormData) => {
    setError(null);
    try {
      const submitData: CreateReimbursementData = {
        business_id: data.business_id,
        payment_ids: data.payment_ids,
        description: data.description || undefined
      };

      if (useCustomAmount && customAmount) {
        submitData.amount = customAmount;
      }

      if (data.expected_date) {
        const expectedDate = new Date(data.expected_date);
        expectedDate.setHours(12, 0, 0, 0); // Set to noon to avoid timezone issues
        submitData.expected_date = expectedDate.toISOString();
      }

      await onSubmit(submitData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const formatCurrency = (amount: number, currency: string = 'KWD') => {
    return `${currency} ${amount.toFixed(3)}`;
  };

  const selectedBusiness = businesses.find(b => b.id === selectedBusinessId);

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* Business Selection */}
      <div>
        <label htmlFor="business_id" className="block text-sm font-medium text-gray-700">
          Business *
        </label>
        <select
          {...register('business_id')}
          id="business_id"
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">Select a business</option>
          {businesses.map((business) => (
            <option key={business.id} value={business.id}>
              {business.name}
            </option>
          ))}
        </select>
        {errors.business_id && (
          <p className="mt-1 text-sm text-red-600">{errors.business_id.message}</p>
        )}
        {selectedBusiness && (
          <p className="mt-1 text-sm text-gray-500">
            Reimbursement timeline: {selectedBusiness.reimbursement_timeline || 30} days
          </p>
        )}
      </div>

      {/* Payment Selection */}
      {selectedBusinessId && (
        <div>
          <div className="flex items-center justify-between mb-3">
            <label className="block text-sm font-medium text-gray-700">
              Select Payments *
            </label>
            {businessPayments.length > 0 && (
              <button
                type="button"
                onClick={handleSelectAllPayments}
                className="text-sm text-blue-600 hover:text-blue-500"
              >
                {selectedPayments.size === businessPayments.length ? 'Deselect All' : 'Select All'}
              </button>
            )}
          </div>

          {businessPayments.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <p className="text-gray-500">No completed payments found for this business.</p>
            </div>
          ) : (
            <div className="border border-gray-300 rounded-lg max-h-64 overflow-y-auto">
              <div className="divide-y divide-gray-200">
                {businessPayments.map((payment) => (
                  <div
                    key={payment.id}
                    className="flex items-center p-3 hover:bg-gray-50"
                  >
                    <input
                      type="checkbox"
                      id={`payment-${payment.id}`}
                      checked={selectedPayments.has(payment.id)}
                      onChange={(e) => handlePaymentSelection(payment.id, e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label
                      htmlFor={`payment-${payment.id}`}
                      className="ml-3 flex-1 cursor-pointer"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {formatCurrency(payment.amount, payment.currency)}
                          </p>
                          <p className="text-sm text-gray-500">
                            {payment.payment_method} • {new Date(payment.payment_date).toLocaleDateString()}
                          </p>
                          {payment.reference_number && (
                            <p className="text-xs text-gray-400">
                              Ref: {payment.reference_number}
                            </p>
                          )}
                        </div>
                        <div className="text-right">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            payment.status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {payment.status}
                          </span>
                        </div>
                      </div>
                    </label>
                  </div>
                ))}
              </div>
            </div>
          )}

          {errors.payment_ids && (
            <p className="mt-1 text-sm text-red-600">{errors.payment_ids.message}</p>
          )}

          {/* Selected Payments Summary */}
          {selectedPayments.size > 0 && (
            <div className="mt-3 p-3 bg-blue-50 rounded-md">
              <p className="text-sm text-blue-800">
                {selectedPayments.size} payment(s) selected • Total: {formatCurrency(totalAmount)}
              </p>
            </div>
          )}
        </div>
      )}

      {/* Amount Section */}
      {selectedPayments.size > 0 && (
        <div>
          <div className="flex items-center justify-between mb-3">
            <label className="block text-sm font-medium text-gray-700">
              Reimbursement Amount
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={useCustomAmount}
                onChange={handleCustomAmountToggle}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-600">Custom amount</span>
            </label>
          </div>

          {useCustomAmount ? (
            <div>
              <input
                type="number"
                step="0.001"
                min="0"
                max={totalAmount}
                value={customAmount || ''}
                onChange={(e) => {
                  const value = parseFloat(e.target.value) || 0;
                  setCustomAmount(value);
                  setValue('amount', value);
                }}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter custom amount"
              />
              <p className="mt-1 text-sm text-gray-500">
                Maximum: {formatCurrency(totalAmount)} (total of selected payments)
              </p>
            </div>
          ) : (
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <span className="text-sm text-gray-600">Total amount of selected payments:</span>
              <span className="text-lg font-semibold text-gray-900">
                {formatCurrency(totalAmount)}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Expected Date */}
      <div>
        <label htmlFor="expected_date" className="block text-sm font-medium text-gray-700">
          Expected Reimbursement Date
        </label>
        <input
          {...register('expected_date')}
          type="date"
          id="expected_date"
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        />
        {errors.expected_date && (
          <p className="mt-1 text-sm text-red-600">{errors.expected_date.message}</p>
        )}
      </div>

      {/* Description */}
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
          Description
        </label>
        <textarea
          {...register('description')}
          id="description"
          rows={3}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="Optional description for this reimbursement request..."
        />
        {errors.description && (
          <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isLoading || selectedPayments.size === 0}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Creating...' : 'Create Reimbursement'}
        </button>
      </div>
    </form>
  );
};