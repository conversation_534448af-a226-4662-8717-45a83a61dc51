import { Request, Response, NextFunction } from 'express';
import { config } from '../config/config';

interface PerformanceMetrics {
  totalRequests: number;
  avgResponseTime: number;
  slowRequests: number;
  errorRequests: number;
  lastReset: Date;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    totalRequests: 0,
    avgResponseTime: 0,
    slowRequests: 0,
    errorRequests: 0,
    lastReset: new Date()
  };

  private responseTimes: number[] = [];
  private readonly maxSamples = 1000;

  addResponseTime(time: number, statusCode: number) {
    this.metrics.totalRequests++;
    this.responseTimes.push(time);

    // Keep only recent samples
    if (this.responseTimes.length > this.maxSamples) {
      this.responseTimes.shift();
    }

    // Calculate average
    this.metrics.avgResponseTime = this.responseTimes.reduce((a, b) => a + b, 0) / this.responseTimes.length;

    // Count slow requests (>5s)
    if (time > 5000) {
      this.metrics.slowRequests++;
    }

    // Count error requests
    if (statusCode >= 400) {
      this.metrics.errorRequests++;
    }
  }

  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  reset() {
    this.metrics = {
      totalRequests: 0,
      avgResponseTime: 0,
      slowRequests: 0,
      errorRequests: 0,
      lastReset: new Date()
    };
    this.responseTimes = [];
  }
}

const performanceMonitor = new PerformanceMonitor();

/**
 * Performance monitoring middleware
 */
export const performanceTracking = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();

  // Track response completion
  res.on('finish', () => {
    const responseTime = Date.now() - startTime;
    performanceMonitor.addResponseTime(responseTime, res.statusCode);

    // Log performance warnings
    if (responseTime > 5000) {
      console.warn(`🐌 SLOW RESPONSE: ${req.method} ${req.originalUrl} took ${responseTime}ms`);
    }

    // Add performance headers for debugging
    if (config.NODE_ENV === 'development') {
      res.setHeader('X-Response-Time', `${responseTime}ms`);
    }
  });

  next();
};

/**
 * Memory usage monitoring
 */
export const memoryMonitoring = (req: Request, res: Response, next: NextFunction) => {
  if (config.NODE_ENV === 'production') {
    const memUsage = process.memoryUsage();
    const memUsageMB = {
      rss: Math.round(memUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024)
    };

    // Log high memory usage
    if (memUsageMB.heapUsed > 512) { // 512MB threshold
      console.warn(`🚨 HIGH MEMORY USAGE:`, memUsageMB);
    }

    // Force garbage collection if memory is very high (requires --expose-gc flag)
    if (memUsageMB.heapUsed > 1024 && global.gc) {
      global.gc();
      console.log('🗑️ Forced garbage collection due to high memory usage');
    }
  }

  next();
};

/**
 * Response caching middleware for static data
 */
export const cacheControl = (duration: number = 3600) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Only cache GET requests
    if (req.method === 'GET') {
      // Set cache headers
      res.setHeader('Cache-Control', `public, max-age=${duration}`);
      res.setHeader('Expires', new Date(Date.now() + duration * 1000).toUTCString());
      
      // Add ETag for cache validation
      const etag = `"${Date.now()}"`;
      res.setHeader('ETag', etag);
      
      // Check if client has cached version
      if (req.headers['if-none-match'] === etag) {
        return res.status(304).end();
      }
    }

    next();
  };
};

/**
 * Request timeout middleware
 */
export const requestTimeout = (timeout: number = 30000) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const timer = setTimeout(() => {
      if (!res.headersSent) {
        console.error(`⏰ REQUEST TIMEOUT: ${req.method} ${req.originalUrl} after ${timeout}ms`);
        res.status(504).json({
          success: false,
          message: 'Request timeout'
        });
      }
    }, timeout);

    // Clear timeout when response finishes
    res.on('finish', () => {
      clearTimeout(timer);
    });

    next();
  };
};

/**
 * Health check metrics endpoint
 */
export const healthMetrics = (req: Request, res: Response) => {
  const metrics = performanceMonitor.getMetrics();
  const memUsage = process.memoryUsage();
  const uptime = process.uptime();

  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m ${Math.floor(uptime % 60)}s`,
    performance: {
      totalRequests: metrics.totalRequests,
      avgResponseTime: Math.round(metrics.avgResponseTime),
      slowRequests: metrics.slowRequests,
      errorRequests: metrics.errorRequests,
      errorRate: metrics.totalRequests > 0 ? (metrics.errorRequests / metrics.totalRequests * 100).toFixed(2) + '%' : '0%'
    },
    memory: {
      rss: `${Math.round(memUsage.rss / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
      heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
      external: `${Math.round(memUsage.external / 1024 / 1024)}MB`
    },
    nodeVersion: process.version,
    platform: process.platform,
    environment: config.NODE_ENV
  });
};

/**
 * Graceful shutdown handler
 */
export const gracefulShutdown = (server: any) => {
  const shutdown = (signal: string) => {
    console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);
    
    server.close((err: any) => {
      if (err) {
        console.error('❌ Error during server shutdown:', err);
        process.exit(1);
      }
      
      console.log('✅ Server closed successfully');
      
      // Close database connections, clear timers, etc.
      // ExchangeRateJob.stop(); // Uncomment if needed
      
      process.exit(0);
    });
    
    // Force shutdown after 30 seconds
    setTimeout(() => {
      console.error('⚠️ Forced shutdown after 30 seconds');
      process.exit(1);
    }, 30000);
  };

  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGINT', () => shutdown('SIGINT'));
};

/**
 * Process error handlers
 */
export const setupProcessHandlers = () => {
  process.on('unhandledRejection', (reason, promise) => {
    console.error('🚨 Unhandled Rejection at:', promise, 'reason:', reason);
    // Don't exit the process in production, just log
    if (config.NODE_ENV !== 'production') {
      process.exit(1);
    }
  });

  process.on('uncaughtException', (error) => {
    console.error('🚨 Uncaught Exception:', error);
    // Exit the process as this is a serious error
    process.exit(1);
  });

  process.on('warning', (warning) => {
    console.warn('⚠️ Node.js Warning:', warning.name, warning.message);
  });
};

export { performanceMonitor };