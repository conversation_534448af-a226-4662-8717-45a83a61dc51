import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useBusiness } from '../../contexts/BusinessContext';
import { BusinessModal } from './BusinessModal';
import { BusinessForm } from './BusinessForm';
import { DeleteBusinessModal } from './DeleteBusinessModal';

interface BusinessFilters {
  search: string;
  sortBy: 'name' | 'created_at' | 'reimbursement_timeline';
  sortOrder: 'asc' | 'desc';
  hasSubscriptions?: boolean;
}

export const BusinessListEnhanced: React.FC = () => {
  const { 
    businesses, 
    loading, 
    error, 
    createBusiness, 
    updateBusiness, 
    deleteBusiness,
    searchBusinesses,
    getBusinessStats
  } = useBusiness();

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingBusiness, setEditingBusiness] = useState(null);
  const [deletingBusiness, setDeletingBusiness] = useState(null);
  const [filters, setFilters] = useState<BusinessFilters>({
    search: '',
    sortBy: 'name',
    sortOrder: 'asc'
  });
  const [businessStats, setBusinessStats] = useState<{ [key: string]: any }>({});
  const [statsLoading, setStatsLoading] = useState<{ [key: string]: boolean }>({});

  // Filter and sort businesses
  const filteredAndSortedBusinesses = React.useMemo(() => {
    let filtered = businesses.filter(business => {
      const matchesSearch = business.name.toLowerCase().includes(filters.search.toLowerCase()) ||
                           business.description?.toLowerCase().includes(filters.search.toLowerCase()) ||
                           business.contact_email?.toLowerCase().includes(filters.search.toLowerCase());
      
      return matchesSearch;
    });

    // Sort businesses
    filtered.sort((a, b) => {
      let aValue, bValue;
      
      switch (filters.sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'created_at':
          aValue = new Date(a.created_at).getTime();
          bValue = new Date(b.created_at).getTime();
          break;
        case 'reimbursement_timeline':
          aValue = a.reimbursement_timeline;
          bValue = b.reimbursement_timeline;
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return filters.sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return filters.sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [businesses, filters]);

  // Load stats for visible businesses
  useEffect(() => {
    filteredAndSortedBusinesses.forEach(async (business) => {
      if (!businessStats[business.id] && !statsLoading[business.id]) {
        setStatsLoading(prev => ({ ...prev, [business.id]: true }));
        try {
          const stats = await getBusinessStats(business.id);
          setBusinessStats(prev => ({ ...prev, [business.id]: stats }));
        } catch (error) {
          console.error(`Failed to load stats for business ${business.id}:`, error);
        } finally {
          setStatsLoading(prev => ({ ...prev, [business.id]: false }));
        }
      }
    });
  }, [filteredAndSortedBusinesses, businessStats, statsLoading, getBusinessStats]);

  const handleCreate = async (businessData: any) => {
    try {
      await createBusiness(businessData);
      setIsCreateModalOpen(false);
    } catch (error) {
      console.error('Failed to create business:', error);
    }
  };

  const handleEdit = async (updates: any) => {
    if (!editingBusiness) return;
    
    try {
      await updateBusiness(editingBusiness.id, updates);
      setEditingBusiness(null);
    } catch (error) {
      console.error('Failed to update business:', error);
    }
  };

  const handleDelete = async () => {
    if (!deletingBusiness) return;
    
    try {
      await deleteBusiness(deletingBusiness.id);
      setDeletingBusiness(null);
    } catch (error) {
      console.error('Failed to delete business:', error);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({ ...prev, search: e.target.value }));
  };

  const handleSortChange = (sortBy: BusinessFilters['sortBy']) => {
    setFilters(prev => ({
      ...prev,
      sortBy,
      sortOrder: prev.sortBy === sortBy && prev.sortOrder === 'asc' ? 'desc' : 'asc'
    }));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Businesses</h1>
          <p className="text-gray-600">Manage your business accounts and subscriptions</p>
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Add Business
        </button>
      </div>

      {/* Filters and Search */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Search */}
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <input
              type="text"
              id="search"
              value={filters.search}
              onChange={handleSearchChange}
              placeholder="Search businesses..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Sort By */}
          <div>
            <label htmlFor="sortBy" className="block text-sm font-medium text-gray-700 mb-1">
              Sort By
            </label>
            <select
              id="sortBy"
              value={filters.sortBy}
              onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as BusinessFilters['sortBy'] }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="name">Name</option>
              <option value="created_at">Date Created</option>
              <option value="reimbursement_timeline">Reimbursement Timeline</option>
            </select>
          </div>

          {/* Sort Order */}
          <div>
            <label htmlFor="sortOrder" className="block text-sm font-medium text-gray-700 mb-1">
              Order
            </label>
            <select
              id="sortOrder"
              value={filters.sortOrder}
              onChange={(e) => setFilters(prev => ({ ...prev, sortOrder: e.target.value as 'asc' | 'desc' }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="asc">Ascending</option>
              <option value="desc">Descending</option>
            </select>
          </div>
        </div>

        {/* Results Summary */}
        <div className="mt-4 text-sm text-gray-600">
          Showing {filteredAndSortedBusinesses.length} of {businesses.length} businesses
        </div>
      </div>

      {/* Business Grid */}
      {filteredAndSortedBusinesses.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-12 text-center">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No businesses found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {filters.search ? 'Try adjusting your search criteria.' : 'Get started by creating your first business.'}
          </p>
          {!filters.search && (
            <div className="mt-6">
              <button
                onClick={() => setIsCreateModalOpen(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Add Business
              </button>
            </div>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAndSortedBusinesses.map((business) => {
            const stats = businessStats[business.id];
            const isStatsLoading = statsLoading[business.id];

            return (
              <div key={business.id} className="bg-white shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900 truncate">
                      {business.name}
                    </h3>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setEditingBusiness(business)}
                        className="text-gray-400 hover:text-gray-600"
                        title="Edit business"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                      <button
                        onClick={() => setDeletingBusiness(business)}
                        className="text-gray-400 hover:text-red-600"
                        title="Delete business"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>

                  {business.description && (
                    <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                      {business.description}
                    </p>
                  )}

                  {/* Business Stats */}
                  {isStatsLoading ? (
                    <div className="animate-pulse">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="h-8 bg-gray-200 rounded"></div>
                        <div className="h-8 bg-gray-200 rounded"></div>
                      </div>
                    </div>
                  ) : stats ? (
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-lg font-semibold text-blue-600">{stats.totalSubscriptions || 0}</div>
                        <div className="text-xs text-gray-500">Subscriptions</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-green-600">{stats.activeSubscriptions || 0}</div>
                        <div className="text-xs text-gray-500">Active</div>
                      </div>
                    </div>
                  ) : null}

                  {/* Business Details */}
                  <div className="space-y-2 text-sm text-gray-600 mb-4">
                    {business.contact_email && (
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        <span className="truncate">{business.contact_email}</span>
                      </div>
                    )}
                    {business.contact_phone && (
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        <span>{business.contact_phone}</span>
                      </div>
                    )}
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>{business.reimbursement_timeline} days reimbursement</span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-2">
                    <Link
                      to={`/businesses/${business.id}`}
                      className="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      View Details
                    </Link>
                    <button
                      onClick={() => setEditingBusiness(business)}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      Edit
                    </button>
                  </div>
                </div>

                {/* Footer with creation date */}
                <div className="bg-gray-50 px-6 py-3">
                  <div className="text-xs text-gray-500">
                    Created {new Date(business.created_at).toLocaleDateString()}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Create Business Modal */}
      <BusinessModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Create New Business"
      >
        <BusinessForm
          onSubmit={handleCreate}
          onCancel={() => setIsCreateModalOpen(false)}
        />
      </BusinessModal>

      {/* Edit Business Modal */}
      <BusinessModal
        isOpen={!!editingBusiness}
        onClose={() => setEditingBusiness(null)}
        title="Edit Business"
      >
        {editingBusiness && (
          <BusinessForm
            business={editingBusiness}
            onSubmit={handleEdit}
            onCancel={() => setEditingBusiness(null)}
          />
        )}
      </BusinessModal>

      {/* Delete Business Modal */}
      {deletingBusiness && (
        <DeleteBusinessModal
          business={deletingBusiness}
          onConfirm={handleDelete}
          onCancel={() => setDeletingBusiness(null)}
        />
      )}
    </div>
  );
};