import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';

export interface Business {
  id: string;
  user_id: string;
  name: string;
  description: string | null;
  address: string | null;
  contact_email: string | null;
  contact_phone: string | null;
  reimbursement_timeline: number;
  created_at: string;
  updated_at: string;
}

export interface BusinessStats {
  totalSubscriptions: number;
  activeSubscriptions: number;
  paymentsByCurrency: Record<string, number>;
  pendingReimbursements: number;
}

interface BusinessContextType {
  businesses: Business[];
  currentBusiness: Business | null;
  loading: boolean;
  error: string | null;
  fetchBusinesses: () => Promise<void>;
  createBusiness: (businessData: Omit<Business, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => Promise<Business>;
  updateBusiness: (id: string, updates: Partial<Business>) => Promise<Business>;
  deleteBusiness: (id: string) => Promise<void>;
  setCurrentBusiness: (business: Business | null) => void;
  getBusinessStats: (id: string) => Promise<BusinessStats>;
  searchBusinesses: (searchTerm: string) => Promise<Business[]>;
}

const BusinessContext = createContext<BusinessContextType | undefined>(undefined);

export const useBusiness = () => {
  const context = useContext(BusinessContext);
  if (context === undefined) {
    throw new Error('useBusiness must be used within a BusinessProvider');
  }
  return context;
};

interface BusinessProviderProps {
  children: ReactNode;
}

export const BusinessProvider: React.FC<BusinessProviderProps> = ({ children }) => {
  const { user, session } = useAuth();
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [currentBusiness, setCurrentBusiness] = useState<Business | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

  const getAuthHeaders = () => ({
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${session?.access_token}`
  });

  const fetchBusinesses = async () => {
    if (!user || !session) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_BASE_URL}/businesses`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to fetch businesses');
      }

      const result = await response.json();
      setBusinesses(result.data);

      // Set current business from localStorage or first business
      const savedBusinessId = localStorage.getItem('currentBusinessId');
      if (savedBusinessId) {
        const savedBusiness = result.data.find((b: Business) => b.id === savedBusinessId);
        if (savedBusiness) {
          setCurrentBusiness(savedBusiness);
        }
      } else if (result.data.length > 0) {
        setCurrentBusiness(result.data[0]);
        localStorage.setItem('currentBusinessId', result.data[0].id);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch businesses');
    } finally {
      setLoading(false);
    }
  };

  const createBusiness = async (businessData: Omit<Business, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<Business> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/businesses`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(businessData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create business');
      }

      const result = await response.json();
      const newBusiness = result.data;

      setBusinesses(prev => [newBusiness, ...prev]);

      // Set as current business if it's the first one
      if (businesses.length === 0) {
        setCurrentBusiness(newBusiness);
        localStorage.setItem('currentBusinessId', newBusiness.id);
      }

      return newBusiness;
    } catch (err) {
      throw err;
    }
  };

  const updateBusiness = async (id: string, updates: Partial<Business>): Promise<Business> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/businesses/${id}`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(updates)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update business');
      }

      const result = await response.json();
      const updatedBusiness = result.data;

      setBusinesses(prev => prev.map(b => b.id === id ? updatedBusiness : b));

      // Update current business if it's the one being updated
      if (currentBusiness?.id === id) {
        setCurrentBusiness(updatedBusiness);
      }

      return updatedBusiness;
    } catch (err) {
      throw err;
    }
  };

  const deleteBusiness = async (id: string): Promise<void> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/businesses/${id}`, {
        method: 'DELETE',
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete business');
      }

      setBusinesses(prev => prev.filter(b => b.id !== id));

      // Update current business if the deleted one was current
      if (currentBusiness?.id === id) {
        const remainingBusinesses = businesses.filter(b => b.id !== id);
        if (remainingBusinesses.length > 0) {
          setCurrentBusiness(remainingBusinesses[0]);
          localStorage.setItem('currentBusinessId', remainingBusinesses[0].id);
        } else {
          setCurrentBusiness(null);
          localStorage.removeItem('currentBusinessId');
        }
      }
    } catch (err) {
      throw err;
    }
  };

  const handleSetCurrentBusiness = (business: Business | null) => {
    setCurrentBusiness(business);
    if (business) {
      localStorage.setItem('currentBusinessId', business.id);
    } else {
      localStorage.removeItem('currentBusinessId');
    }
  };

  const getBusinessStats = async (id: string): Promise<BusinessStats> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/businesses/${id}/stats`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to fetch business statistics');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      throw err instanceof Error ? err : new Error('Failed to fetch business statistics');
    }
  };

  const searchBusinesses = async (searchTerm: string): Promise<Business[]> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/businesses?q=${encodeURIComponent(searchTerm)}`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to search businesses');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      throw err instanceof Error ? err : new Error('Failed to search businesses');
    }
  };

  // Fetch businesses when user logs in
  useEffect(() => {
    if (user && session) {
      fetchBusinesses();
    } else {
      setBusinesses([]);
      setCurrentBusiness(null);
    }
  }, [user, session]);

  const value: BusinessContextType = {
    businesses,
    currentBusiness,
    loading,
    error,
    fetchBusinesses,
    createBusiness,
    updateBusiness,
    deleteBusiness,
    setCurrentBusiness: handleSetCurrentBusiness,
    getBusinessStats,
    searchBusinesses
  };

  return (
    <BusinessContext.Provider value={value}>
      {children}
    </BusinessContext.Provider>
  );
};