import React, { useState } from 'react';
import { usePayment, PaymentFilters } from '../../contexts/PaymentContext';
import { useAuth } from '../../contexts/AuthContext';

interface ExportOptions {
  format: 'csv' | 'pdf' | 'excel';
  dateRange: {
    from: string;
    to: string;
  };
  filters: PaymentFilters;
  includeAttachments: boolean;
  groupBy: 'none' | 'status' | 'currency' | 'payment_method' | 'subscription';
  columns: string[];
}

interface PaymentExportProps {
  isOpen: boolean;
  onClose: () => void;
}

const AVAILABLE_COLUMNS = [
  { key: 'payment_date', label: 'Payment Date' },
  { key: 'amount', label: 'Amount' },
  { key: 'currency', label: 'Currency' },
  { key: 'payment_method', label: 'Payment Method' },
  { key: 'reference_number', label: 'Reference Number' },
  { key: 'status', label: 'Status' },
  { key: 'subscription_name', label: 'Subscription' },
  { key: 'notes', label: 'Notes' },
  { key: 'created_at', label: 'Created Date' }
];

export const PaymentExport: React.FC<PaymentExportProps> = ({ isOpen, onClose }) => {
  const { session } = useAuth();
  const [exporting, setExporting] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'csv',
    dateRange: {
      from: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0], // Start of year
      to: new Date().toISOString().split('T')[0] // Today
    },
    filters: {},
    includeAttachments: false,
    groupBy: 'none',
    columns: ['payment_date', 'amount', 'currency', 'payment_method', 'status', 'subscription_name']
  });

  const handleExport = async () => {
    if (!session?.access_token) return;

    setExporting(true);
    try {
      const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
      
      const exportData = {
        ...exportOptions,
        filters: {
          ...exportOptions.filters,
          date_from: exportOptions.dateRange.from,
          date_to: exportOptions.dateRange.to
        }
      };

      const response = await fetch(`${API_BASE_URL}/payments/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify(exportData)
      });

      if (!response.ok) {
        throw new Error('Export failed');
      }

      // Handle file download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `payments-export-${timestamp}.${exportOptions.format}`;
      link.download = filename;
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      onClose();
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  const updateFilters = (key: keyof PaymentFilters, value: string) => {
    setExportOptions(prev => ({
      ...prev,
      filters: {
        ...prev.filters,
        [key]: value || undefined
      }
    }));
  };

  const toggleColumn = (columnKey: string) => {
    setExportOptions(prev => ({
      ...prev,
      columns: prev.columns.includes(columnKey)
        ? prev.columns.filter(col => col !== columnKey)
        : [...prev.columns, columnKey]
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-medium text-gray-900">Export Payments</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="space-y-6">
            {/* Export Format */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Export Format</label>
              <div className="flex space-x-4">
                {['csv', 'pdf', 'excel'].map(format => (
                  <label key={format} className="flex items-center">
                    <input
                      type="radio"
                      name="format"
                      value={format}
                      checked={exportOptions.format === format}
                      onChange={(e) => setExportOptions(prev => ({ ...prev, format: e.target.value as any }))}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-700 capitalize">{format}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">From</label>
                  <input
                    type="date"
                    value={exportOptions.dateRange.from}
                    onChange={(e) => setExportOptions(prev => ({
                      ...prev,
                      dateRange: { ...prev.dateRange, from: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">To</label>
                  <input
                    type="date"
                    value={exportOptions.dateRange.to}
                    onChange={(e) => setExportOptions(prev => ({
                      ...prev,
                      dateRange: { ...prev.dateRange, to: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
              </div>
            </div>

            {/* Filters */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Filters</label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Status</label>
                  <select
                    value={exportOptions.filters.status || ''}
                    onChange={(e) => updateFilters('status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="">All Statuses</option>
                    <option value="completed">Completed</option>
                    <option value="pending">Pending</option>
                    <option value="failed">Failed</option>
                    <option value="refunded">Refunded</option>
                  </select>
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Currency</label>
                  <select
                    value={exportOptions.filters.currency || ''}
                    onChange={(e) => updateFilters('currency', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="">All Currencies</option>
                    <option value="USD">USD</option>
                    <option value="GBP">GBP</option>
                    <option value="KWD">KWD</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Group By */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Group By</label>
              <select
                value={exportOptions.groupBy}
                onChange={(e) => setExportOptions(prev => ({ ...prev, groupBy: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="none">No Grouping</option>
                <option value="status">Status</option>
                <option value="currency">Currency</option>
                <option value="payment_method">Payment Method</option>
                <option value="subscription">Subscription</option>
              </select>
            </div>

            {/* Columns */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Include Columns</label>
              <div className="grid grid-cols-2 gap-2">
                {AVAILABLE_COLUMNS.map(column => (
                  <label key={column.key} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={exportOptions.columns.includes(column.key)}
                      onChange={() => toggleColumn(column.key)}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-700">{column.label}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Additional Options */}
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={exportOptions.includeAttachments}
                  onChange={(e) => setExportOptions(prev => ({ ...prev, includeAttachments: e.target.checked }))}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">Include attachment information</span>
              </label>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 mt-8">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
            >
              Cancel
            </button>
            <button
              onClick={handleExport}
              disabled={exporting || exportOptions.columns.length === 0}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {exporting ? 'Exporting...' : 'Export'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
