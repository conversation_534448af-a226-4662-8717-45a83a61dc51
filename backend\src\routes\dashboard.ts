import { Router } from 'express';
import { asyncHandler } from '../middleware/errorHandler';

const router = Router();

// GET /api/dashboard/metrics
router.get('/metrics', asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'Get dashboard metrics endpoint - to be implemented',
    data: {
      totalSubscriptions: 0,
      upcomingPayments: 0,
      pendingReimbursements: 0,
      monthlySpending: 0
    }
  });
}));

// GET /api/dashboard/charts
router.get('/charts', asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'Get dashboard charts endpoint - to be implemented',
    data: {
      paymentTrends: [],
      currencyDistribution: [],
      businessComparison: []
    }
  });
}));

export { router as dashboardRoutes };