import { supabaseAdmin } from '../config/supabase';
import { createError } from '../middleware/errorHandler';

export interface Business {
  id: string;
  user_id: string;
  name: string;
  description: string | null;
  address: string | null;
  contact_email: string | null;
  contact_phone: string | null;
  reimbursement_timeline: number;
  created_at: string;
  updated_at: string;
}

export interface CreateBusinessData {
  name: string;
  description?: string;
  address?: string;
  contact_email?: string;
  contact_phone?: string;
  reimbursement_timeline?: number;
}

export interface UpdateBusinessData extends Partial<CreateBusinessData> {}

export interface BusinessStats {
  totalSubscriptions: number;
  activeSubscriptions: number;
  totalPayments: number;
  pendingReimbursements: number;
  monthlySpending: number;
  paymentsByCurrency: Record<string, number>;
}

export class BusinessService {
  /**
   * Get all businesses for a user
   */
  static async getUserBusinesses(userId: string): Promise<Business[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('businesses')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        throw createError(`Failed to fetch businesses: ${error.message}`, 500);
      }

      return data || [];
    } catch (error) {
      console.error('Failed to get user businesses:', error);
      throw error;
    }
  }

  /**
   * Get a specific business by ID
   */
  static async getBusinessById(businessId: string, userId: string): Promise<Business> {
    try {
      const { data, error } = await supabaseAdmin
        .from('businesses')
        .select('*')
        .eq('id', businessId)
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          throw createError('Business not found', 404);
        }
        throw createError(`Failed to fetch business: ${error.message}`, 500);
      }

      return data;
    } catch (error) {
      console.error('Failed to get business by ID:', error);
      throw error;
    }
  }

  /**
   * Create a new business
   */
  static async createBusiness(userId: string, businessData: CreateBusinessData): Promise<Business> {
    try {
      const { data, error } = await supabaseAdmin
        .from('businesses')
        .insert({
          user_id: userId,
          name: businessData.name,
          description: businessData.description || null,
          address: businessData.address || null,
          contact_email: businessData.contact_email || null,
          contact_phone: businessData.contact_phone || null,
          reimbursement_timeline: businessData.reimbursement_timeline || 30
        })
        .select()
        .single();

      if (error) {
        throw createError(`Failed to create business: ${error.message}`, 400);
      }

      return data;
    } catch (error) {
      console.error('Failed to create business:', error);
      throw error;
    }
  }

  /**
   * Update an existing business
   */
  static async updateBusiness(
    businessId: string,
    userId: string,
    updates: UpdateBusinessData
  ): Promise<Business> {
    try {
      // First verify the business belongs to the user
      await this.getBusinessById(businessId, userId);

      const { data, error } = await supabaseAdmin
        .from('businesses')
        .update(updates)
        .eq('id', businessId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        throw createError(`Failed to update business: ${error.message}`, 400);
      }

      return data;
    } catch (error) {
      console.error('Failed to update business:', error);
      throw error;
    }
  }

  /**
   * Delete a business and all associated data
   */
  static async deleteBusiness(businessId: string, userId: string): Promise<void> {
    try {
      // First verify the business belongs to the user
      await this.getBusinessById(businessId, userId);

      // Check if business has associated subscriptions
      const { data: subscriptions, error: subscriptionsError } = await supabaseAdmin
        .from('subscriptions')
        .select('id')
        .eq('business_id', businessId)
        .limit(1);

      if (subscriptionsError) {
        throw createError(`Failed to check subscriptions: ${subscriptionsError.message}`, 500);
      }

      if (subscriptions && subscriptions.length > 0) {
        throw createError(
          'Cannot delete business with existing subscriptions. Please delete all subscriptions first.',
          400
        );
      }

      // Check if business has associated reimbursements
      const { data: reimbursements, error: reimbursementsError } = await supabaseAdmin
        .from('reimbursements')
        .select('id')
        .eq('business_id', businessId)
        .limit(1);

      if (reimbursementsError) {
        throw createError(`Failed to check reimbursements: ${reimbursementsError.message}`, 500);
      }

      if (reimbursements && reimbursements.length > 0) {
        throw createError(
          'Cannot delete business with existing reimbursements. Please delete all reimbursements first.',
          400
        );
      }

      // Delete the business
      const { error } = await supabaseAdmin
        .from('businesses')
        .delete()
        .eq('id', businessId)
        .eq('user_id', userId);

      if (error) {
        throw createError(`Failed to delete business: ${error.message}`, 500);
      }
    } catch (error) {
      console.error('Failed to delete business:', error);
      throw error;
    }
  }

  /**
   * Get business statistics
   */
  static async getBusinessStats(businessId: string, userId: string) {
    try {
      // Verify business belongs to user
      await this.getBusinessById(businessId, userId);

      // Get subscription count
      const { count: subscriptionCount, error: subscriptionError } = await supabaseAdmin
        .from('subscriptions')
        .select('*', { count: 'exact', head: true })
        .eq('business_id', businessId);

      if (subscriptionError) {
        throw createError(`Failed to get subscription count: ${subscriptionError.message}`, 500);
      }

      // Get active subscription count
      const { count: activeSubscriptionCount, error: activeSubscriptionError } = await supabaseAdmin
        .from('subscriptions')
        .select('*', { count: 'exact', head: true })
        .eq('business_id', businessId)
        .eq('status', 'active');

      if (activeSubscriptionError) {
        throw createError(`Failed to get active subscription count: ${activeSubscriptionError.message}`, 500);
      }

      // Get total payments for this business
      const { data: payments, error: paymentsError } = await supabaseAdmin
        .from('payments')
        .select('amount, currency')
        .in('subscription_id', 
          supabaseAdmin
            .from('subscriptions')
            .select('id')
            .eq('business_id', businessId)
        );

      if (paymentsError) {
        throw createError(`Failed to get payments: ${paymentsError.message}`, 500);
      }

      // Calculate total payments by currency
      const paymentsByCurrency = (payments || []).reduce((acc, payment) => {
        acc[payment.currency] = (acc[payment.currency] || 0) + payment.amount;
        return acc;
      }, {} as Record<string, number>);

      // Get pending reimbursements
      const { count: pendingReimbursements, error: reimbursementError } = await supabaseAdmin
        .from('reimbursements')
        .select('*', { count: 'exact', head: true })
        .eq('business_id', businessId)
        .eq('status', 'pending');

      if (reimbursementError) {
        throw createError(`Failed to get reimbursement count: ${reimbursementError.message}`, 500);
      }

      return {
        totalSubscriptions: subscriptionCount || 0,
        activeSubscriptions: activeSubscriptionCount || 0,
        paymentsByCurrency,
        pendingReimbursements: pendingReimbursements || 0
      };
    } catch (error) {
      console.error('Failed to get business stats:', error);
      throw error;
    }
  }

  /**
   * Search businesses by name
   */
  static async searchBusinesses(userId: string, searchTerm: string): Promise<Business[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('businesses')
        .select('*')
        .eq('user_id', userId)
        .ilike('name', `%${searchTerm}%`)
        .order('created_at', { ascending: false });

      if (error) {
        throw createError(`Failed to search businesses: ${error.message}`, 500);
      }

      return data || [];
    } catch (error) {
      console.error('Failed to search businesses:', error);
      throw error;
    }
  }
}