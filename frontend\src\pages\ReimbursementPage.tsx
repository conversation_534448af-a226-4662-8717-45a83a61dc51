import React, { useState, useEffect } from 'react';
import { ReimbursementTable } from '../components/reimbursement/ReimbursementTable';
import { ReimbursementModal } from '../components/reimbursement/ReimbursementModal';
import { useReimbursement, Reimbursement } from '../contexts/ReimbursementContext';
import { useBusiness } from '../contexts/BusinessContext';

export const ReimbursementPage: React.FC = () => {
  const {
    reimbursements,
    loading,
    error,
    fetchReimbursements,
    updateReimbursementStatus,
    deleteReimbursement
  } = useReimbursement();
  
  const { currentBusiness } = useBusiness();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedReimbursement, setSelectedReimbursement] = useState<Reimbursement | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [filters, setFilters] = useState({
    business_id: '',
    status: '' as '' | 'pending' | 'approved' | 'received',
    date_from: '',
    date_to: ''
  });

  useEffect(() => {
    // Fetch reimbursements when component mounts or filters change
    const activeFilters = Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== '')
    );
    fetchReimbursements(activeFilters);
  }, [fetchReimbursements, filters]);

  const handleCreateReimbursement = () => {
    setIsModalOpen(true);
  };

  const handleEditReimbursement = (reimbursement: Reimbursement) => {
    setSelectedReimbursement(reimbursement);
    setIsModalOpen(true);
  };

  const handleViewReimbursement = (reimbursement: Reimbursement) => {
    setSelectedReimbursement(reimbursement);
    setIsViewModalOpen(true);
  };

  const handleDeleteReimbursement = async (reimbursement: Reimbursement) => {
    if (window.confirm('Are you sure you want to delete this reimbursement? This action cannot be undone.')) {
      try {
        await deleteReimbursement(reimbursement.id);
      } catch (error) {
        console.error('Failed to delete reimbursement:', error);
        alert('Failed to delete reimbursement. Please try again.');
      }
    }
  };

  const handleStatusChange = async (
    reimbursement: Reimbursement,
    newStatus: 'pending' | 'approved' | 'received'
  ) => {
    if (reimbursement.status === newStatus) return;

    const statusConfirmations = {
      approved: 'Are you sure you want to approve this reimbursement?',
      received: 'Are you sure you want to mark this reimbursement as received?',
      pending: 'Are you sure you want to change this reimbursement back to pending?'
    };

    if (window.confirm(statusConfirmations[newStatus])) {
      try {
        await updateReimbursementStatus(reimbursement.id, newStatus);
      } catch (error) {
        console.error('Failed to update status:', error);
        alert('Failed to update reimbursement status. Please try again.');
      }
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedReimbursement(null);
  };

  const handleViewModalClose = () => {
    setIsViewModalOpen(false);
    setSelectedReimbursement(null);
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Get summary statistics
  const stats = {
    total: reimbursements.length,
    pending: reimbursements.filter(r => r.status === 'pending').length,
    approved: reimbursements.filter(r => r.status === 'approved').length,
    received: reimbursements.filter(r => r.status === 'received').length,
    totalAmount: reimbursements.reduce((sum, r) => sum + r.amount, 0),
    pendingAmount: reimbursements.filter(r => r.status === 'pending').reduce((sum, r) => sum + r.amount, 0)
  };

  const overdue = reimbursements.filter(r => 
    (r.status === 'pending' || r.status === 'approved') && 
    new Date(r.expected_date) < new Date()
  ).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Reimbursements</h1>
          <p className="mt-2 text-sm text-gray-700">
            Manage your reimbursement requests and track their status.
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button
            onClick={handleCreateReimbursement}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            New Reimbursement
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Requests</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.total}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Pending</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stats.pending}
                    {overdue > 0 && (
                      <span className="ml-2 text-sm text-red-600">({overdue} overdue)</span>
                    )}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Approved</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.approved}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Pending Amount</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    KWD {stats.pendingAmount.toFixed(3)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700">
              Status
            </label>
            <select
              id="status-filter"
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="received">Received</option>
            </select>
          </div>

          <div>
            <label htmlFor="date-from" className="block text-sm font-medium text-gray-700">
              From Date
            </label>
            <input
              type="date"
              id="date-from"
              value={filters.date_from}
              onChange={(e) => handleFilterChange('date_from', e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label htmlFor="date-to" className="block text-sm font-medium text-gray-700">
              To Date
            </label>
            <input
              type="date"
              id="date-to"
              value={filters.date_to}
              onChange={(e) => handleFilterChange('date_to', e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div className="flex items-end">
            <button
              onClick={() => setFilters({
                business_id: '',
                status: '',
                date_from: '',
                date_to: ''
              })}
              className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Reimbursements Table */}
      <div className="bg-white shadow rounded-lg">
        <ReimbursementTable
          reimbursements={reimbursements}
          onEdit={handleEditReimbursement}
          onDelete={handleDeleteReimbursement}
          onView={handleViewReimbursement}
          onStatusChange={handleStatusChange}
          loading={loading}
        />
      </div>

      {/* Create/Edit Modal */}
      <ReimbursementModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
      />

      {/* View Modal */}
      {isViewModalOpen && selectedReimbursement && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div
              className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
              onClick={handleViewModalClose}
            />
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Reimbursement Details
                  </h3>
                  <button
                    type="button"
                    onClick={handleViewModalClose}
                    className="bg-white rounded-md text-gray-400 hover:text-gray-600"
                  >
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <span className="font-medium">Reference: </span>
                    {selectedReimbursement.reference_number}
                  </div>
                  <div>
                    <span className="font-medium">Business: </span>
                    {selectedReimbursement.business.name}
                  </div>
                  <div>
                    <span className="font-medium">Amount: </span>
                    KWD {selectedReimbursement.amount.toFixed(3)}
                  </div>
                  <div>
                    <span className="font-medium">Status: </span>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      selectedReimbursement.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      selectedReimbursement.status === 'approved' ? 'bg-blue-100 text-blue-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {selectedReimbursement.status.charAt(0).toUpperCase() + selectedReimbursement.status.slice(1)}
                    </span>
                  </div>
                  {selectedReimbursement.description && (
                    <div>
                      <span className="font-medium">Description: </span>
                      {selectedReimbursement.description}
                    </div>
                  )}
                  <div>
                    <span className="font-medium">Payments ({selectedReimbursement.payments.length}): </span>
                    <ul className="mt-2 space-y-1">
                      {selectedReimbursement.payments.map((payment) => (
                        <li key={payment.id} className="text-sm text-gray-600">
                          {payment.subscription.service_name} - {payment.currency} {payment.amount.toFixed(2)} 
                          ({new Date(payment.payment_date).toLocaleDateString()})
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};