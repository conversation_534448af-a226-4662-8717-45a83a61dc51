import cron from 'node-cron';
import { ExchangeRateService } from '../services/exchangeRateService';

export class ExchangeRateJob {
  private static isRunning = false;

  /**
   * Start the scheduled job to fetch exchange rates daily at 6 AM UTC
   */
  static start() {
    // Run daily at 6:00 AM UTC
    cron.schedule('0 6 * * *', async () => {
      if (this.isRunning) {
        console.log('Exchange rate job already running, skipping...');
        return;
      }

      this.isRunning = true;
      console.log('Starting daily exchange rate fetch job...');

      try {
        await this.fetchAndStoreRates();
        console.log('Exchange rate job completed successfully');
      } catch (error) {
        console.error('Exchange rate job failed:', error);
      } finally {
        this.isRunning = false;
      }
    }, {
      scheduled: true,
      timezone: 'UTC'
    });

    console.log('Exchange rate cron job scheduled to run daily at 6:00 AM UTC');
  }

  /**
   * Run the job manually (for testing or immediate updates)
   */
  static async runManually(): Promise<void> {
    if (this.isRunning) {
      throw new Error('Exchange rate job is already running');
    }

    this.isRunning = true;
    console.log('Running exchange rate job manually...');

    try {
      await this.fetchAndStoreRates();
      console.log('Manual exchange rate job completed successfully');
    } catch (error) {
      console.error('Manual exchange rate job failed:', error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Fetch current rates and store them in database
   */
  private static async fetchAndStoreRates(): Promise<void> {
    try {
      const currentRates = await ExchangeRateService.fetchCurrentRates();
      
      if (Object.keys(currentRates).length === 0) {
        throw new Error('No exchange rates received from API');
      }

      const storedRates = await ExchangeRateService.storeRates(currentRates);
      
      console.log(`Successfully stored ${storedRates.length} exchange rates:`, 
        storedRates.map(rate => `${rate.from_currency}/${rate.to_currency}: ${rate.rate}`).join(', ')
      );

      // Log the rates for monitoring
      for (const rate of storedRates) {
        console.log(`Exchange rate updated: ${rate.from_currency} to ${rate.to_currency} = ${rate.rate} (${rate.date})`);
      }
    } catch (error) {
      console.error('Failed to fetch and store exchange rates:', error);
      throw error;
    }
  }

  /**
   * Stop the scheduled job
   */
  static stop() {
    cron.getTasks().forEach(task => task.stop());
    console.log('Exchange rate cron job stopped');
  }

  /**
   * Get job status
   */
  static getStatus() {
    return {
      isRunning: this.isRunning,
      scheduledTasks: cron.getTasks().size
    };
  }
}