import { createClient } from '@supabase/supabase-js';
import { config } from './config';

// Create Supabase client for general use
export const supabase = createClient(
  config.SUPABASE_URL,
  config.SUPABASE_ANON_KEY
);

// Create Supabase admin client for server-side operations
export const supabaseAdmin = createClient(
  config.SUPABASE_URL,
  config.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Database types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          first_name: string;
          last_name: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          first_name: string;
          last_name: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          first_name?: string;
          last_name?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      businesses: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          description: string | null;
          address: string | null;
          contact_email: string | null;
          contact_phone: string | null;
          reimbursement_timeline: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name: string;
          description?: string | null;
          address?: string | null;
          contact_email?: string | null;
          contact_phone?: string | null;
          reimbursement_timeline?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string;
          description?: string | null;
          address?: string | null;
          contact_email?: string | null;
          contact_phone?: string | null;
          reimbursement_timeline?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      subscriptions: {
        Row: {
          id: string;
          business_id: string;
          service_name: string;
          provider: string;
          amount: number;
          currency: 'USD' | 'GBP' | 'KWD';
          billing_frequency: 'monthly' | 'quarterly' | 'yearly';
          category: string | null;
          status: 'active' | 'inactive' | 'cancelled';
          next_payment_date: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          business_id: string;
          service_name: string;
          provider: string;
          amount: number;
          currency: 'USD' | 'GBP' | 'KWD';
          billing_frequency: 'monthly' | 'quarterly' | 'yearly';
          category?: string | null;
          status?: 'active' | 'inactive' | 'cancelled';
          next_payment_date: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          business_id?: string;
          service_name?: string;
          provider?: string;
          amount?: number;
          currency?: 'USD' | 'GBP' | 'KWD';
          billing_frequency?: 'monthly' | 'quarterly' | 'yearly';
          category?: string | null;
          status?: 'active' | 'inactive' | 'cancelled';
          next_payment_date?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      payments: {
        Row: {
          id: string;
          subscription_id: string;
          amount: number;
          currency: 'USD' | 'GBP' | 'KWD';
          payment_date: string;
          payment_method: string;
          reference_number: string | null;
          notes: string | null;
          status: 'pending' | 'completed' | 'failed' | 'refunded';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          subscription_id: string;
          amount: number;
          currency: 'USD' | 'GBP' | 'KWD';
          payment_date: string;
          payment_method: string;
          reference_number?: string | null;
          notes?: string | null;
          status?: 'pending' | 'completed' | 'failed' | 'refunded';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          subscription_id?: string;
          amount?: number;
          currency?: 'USD' | 'GBP' | 'KWD';
          payment_date?: string;
          payment_method?: string;
          reference_number?: string | null;
          notes?: string | null;
          status?: 'pending' | 'completed' | 'failed' | 'refunded';
          created_at?: string;
          updated_at?: string;
        };
      };
      reimbursements: {
        Row: {
          id: string;
          business_id: string;
          user_id: string;
          reference_number: string;
          amount: number;
          currency: 'KWD';
          description: string | null;
          request_date: string;
          expected_date: string;
          actual_date: string | null;
          status: 'pending' | 'approved' | 'received';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          business_id: string;
          user_id: string;
          reference_number: string;
          amount: number;
          currency?: 'KWD';
          description?: string | null;
          request_date: string;
          expected_date: string;
          actual_date?: string | null;
          status?: 'pending' | 'approved' | 'received';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          business_id?: string;
          user_id?: string;
          reference_number?: string;
          amount?: number;
          currency?: 'KWD';
          description?: string | null;
          request_date?: string;
          expected_date?: string;
          actual_date?: string | null;
          status?: 'pending' | 'approved' | 'received';
          created_at?: string;
          updated_at?: string;
        };
      };
      exchange_rates: {
        Row: {
          id: string;
          date: string;
          from_currency: string;
          to_currency: string;
          rate: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          date: string;
          from_currency: string;
          to_currency: string;
          rate: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          date?: string;
          from_currency?: string;
          to_currency?: string;
          rate?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
  };
}