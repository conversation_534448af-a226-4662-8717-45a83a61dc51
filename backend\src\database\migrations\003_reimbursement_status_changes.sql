-- Create reimbursement_status_changes table for audit trail
CREATE TABLE IF NOT EXISTS public.reimbursement_status_changes (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  reimbursement_id UUID REFERENCES public.reimbursements(id) ON DELETE CASCADE NOT NULL,
  from_status TEXT NOT NULL CHECK (from_status IN ('pending', 'approved', 'received')),
  to_status TEXT NOT NULL CHECK (to_status IN ('pending', 'approved', 'received')),
  changed_by UUID REFERENCES public.users(id) ON DELETE SET NULL,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_reimbursement_status_changes_reimbursement_id 
ON public.reimbursement_status_changes(reimbursement_id);

CREATE INDEX IF NOT EXISTS idx_reimbursement_status_changes_created_at 
ON public.reimbursement_status_changes(created_at);