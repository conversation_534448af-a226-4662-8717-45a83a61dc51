import { supabase, supabaseAdmin } from '../config/supabase';
import { createError } from '../middleware/errorHandler';

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export class AuthService {
  static async register(userData: RegisterData) {
    const { email, password, firstName, lastName } = userData;

    // Register user with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          first_name: firstName,
          last_name: lastName
        }
      }
    });

    if (authError) {
      throw createError(authError.message, 400);
    }

    if (!authData.user) {
      throw createError('Failed to create user', 400);
    }

    // Create user profile in public.users table
    const { error: profileError } = await supabaseAdmin
      .from('users')
      .insert({
        id: authData.user.id,
        email,
        first_name: firstName,
        last_name: lastName
      });

    if (profileError) {
      // If profile creation fails, we should clean up the auth user
      await supabaseAdmin.auth.admin.deleteUser(authData.user.id);
      throw createError('Failed to create user profile', 500);
    }

    return {
      user: authData.user,
      session: authData.session
    };
  }

  static async login(loginData: LoginData) {
    const { email, password } = loginData;

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      throw createError(error.message, 401);
    }

    return {
      user: data.user,
      session: data.session
    };
  }

  static async logout(accessToken: string) {
    const { error } = await supabase.auth.admin.signOut(accessToken);

    if (error) {
      throw createError(error.message, 400);
    }

    return { message: 'Logged out successfully' };
  }

  static async refreshToken(refreshToken: string) {
    const { data, error } = await supabase.auth.refreshSession({
      refresh_token: refreshToken
    });

    if (error) {
      throw createError(error.message, 401);
    }

    return {
      session: data.session
    };
  }

  static async resetPassword(email: string) {
    // First check if user exists
    const { data: existingUser, error: userError } = await supabaseAdmin
      .from('users')
      .select('email')
      .eq('email', email)
      .single();

    if (userError || !existingUser) {
      // Don't reveal if email exists or not for security
      return { message: 'If the email exists, a password reset link has been sent' };
    }

    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.FRONTEND_URL}/reset-password`
    });

    if (error) {
      console.error('Password reset error:', error);
      // Don't reveal the actual error to the user
      return { message: 'If the email exists, a password reset link has been sent' };
    }

    return { message: 'Password reset email sent successfully' };
  }

  static async updatePassword(accessToken: string, newPassword: string) {
    const { error } = await supabase.auth.updateUser({
      password: newPassword
    });

    if (error) {
      throw createError(error.message, 400);
    }

    return { message: 'Password updated successfully' };
  }

  static async getUserProfile(userId: string) {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      throw createError('User not found', 404);
    }

    return data;
  }

  static async updateUserProfile(userId: string, updates: Partial<{
    first_name: string;
    last_name: string;
    email: string;
  }>) {
    // If email is being updated, also update in Supabase Auth
    if (updates.email) {
      const { error: authError } = await supabaseAdmin.auth.admin.updateUserById(
        userId,
        { email: updates.email }
      );

      if (authError) {
        throw createError(`Failed to update email: ${authError.message}`, 400);
      }
    }

    const { data, error } = await supabaseAdmin
      .from('users')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      throw createError('Failed to update profile', 400);
    }

    return data;
  }

  static async changePassword(userId: string, currentPassword: string, newPassword: string) {
    // First verify current password by attempting to sign in
    const { data: user } = await supabaseAdmin
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    if (!user) {
      throw createError('User not found', 404);
    }

    // Verify current password
    const { error: signInError } = await supabase.auth.signInWithPassword({
      email: user.email,
      password: currentPassword
    });

    if (signInError) {
      throw createError('Current password is incorrect', 400);
    }

    // Update password
    const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
      userId,
      { password: newPassword }
    );

    if (updateError) {
      throw createError(`Failed to update password: ${updateError.message}`, 400);
    }

    return { message: 'Password updated successfully' };
  }

  static async deleteAccount(userId: string) {
    // Delete user profile first (cascade will handle related data)
    const { error: profileError } = await supabaseAdmin
      .from('users')
      .delete()
      .eq('id', userId);

    if (profileError) {
      throw createError('Failed to delete user profile', 500);
    }

    // Delete from Supabase Auth
    const { error: authError } = await supabaseAdmin.auth.admin.deleteUser(userId);

    if (authError) {
      console.error('Failed to delete auth user:', authError);
      // Profile is already deleted, so we'll log this but not throw
    }

    return { message: 'Account deleted successfully' };
  }
}