import React, { useState } from 'react';
import { Business, useBusiness } from '../../contexts/BusinessContext';

interface BusinessListProps {
  onSelectBusiness?: (business: Business) => void;
  onEditBusiness?: (business: Business) => void;
  onDeleteBusiness?: (business: Business) => void;
  showActions?: boolean;
}

export const BusinessList: React.FC<BusinessListProps> = ({
  onSelectBusiness,
  onEditBusiness,
  onDeleteBusiness,
  showActions = true
}) => {
  const { businesses, currentBusiness, setCurrentBusiness, loading, error } = useBusiness();
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredBusinesses, setFilteredBusinesses] = useState<Business[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const { searchBusinesses } = useBusiness();

  const handleSearch = async (term: string) => {
    setSearchTerm(term);
    if (term.trim()) {
      setIsSearching(true);
      try {
        const results = await searchBusinesses(term);
        setFilteredBusinesses(results);
      } catch (err) {
        console.error('Search failed:', err);
        setFilteredBusinesses([]);
      } finally {
        setIsSearching(false);
      }
    } else {
      setFilteredBusinesses([]);
    }
  };

  const displayBusinesses = searchTerm.trim() ? filteredBusinesses : businesses;

  const handleSelectBusiness = (business: Business) => {
    setCurrentBusiness(business);
    onSelectBusiness?.(business);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-600 text-sm">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search */}
      <div className="relative">
        <input
          type="text"
          placeholder="Search businesses..."
          value={searchTerm}
          onChange={(e) => handleSearch(e.target.value)}
          className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        {isSearching && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          </div>
        )}
      </div>

      {/* Business List */}
      {displayBusinesses.length === 0 ? (
        <div className="text-center py-8">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            {searchTerm.trim() ? 'No businesses found' : 'No businesses'}
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm.trim() ? 'Try a different search term.' : 'Get started by creating a new business.'}
          </p>
        </div>
      ) : (
        <div className="grid gap-4">
          {displayBusinesses.map((business) => (
            <div
              key={business.id}
              className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                currentBusiness?.id === business.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => handleSelectBusiness(business)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="text-lg font-medium text-gray-900">{business.name}</h3>
                  {business.description && (
                    <p className="mt-1 text-sm text-gray-600">{business.description}</p>
                  )}
                  <div className="mt-2 space-y-1">
                    {business.contact_email && (
                      <p className="text-sm text-gray-500">
                        <span className="font-medium">Email:</span> {business.contact_email}
                      </p>
                    )}
                    {business.contact_phone && (
                      <p className="text-sm text-gray-500">
                        <span className="font-medium">Phone:</span> {business.contact_phone}
                      </p>
                    )}
                    <p className="text-sm text-gray-500">
                      <span className="font-medium">Reimbursement Timeline:</span> {business.reimbursement_timeline} days
                    </p>
                  </div>
                </div>

                {showActions && (
                  <div className="flex space-x-2 ml-4">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onEditBusiness?.(business);
                      }}
                      className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                      title="Edit business"
                    >
                      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteBusiness?.(business);
                      }}
                      className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                      title="Delete business"
                    >
                      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};