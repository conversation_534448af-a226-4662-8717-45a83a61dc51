import React, { useState } from 'react';
import { ReimbursementForm } from './ReimbursementForm';
import { useReimbursement, CreateReimbursementData } from '../../contexts/ReimbursementContext';

interface ReimbursementModalProps {
  isOpen: boolean;
  onClose: () => void;
  preselectedPaymentIds?: string[];
}

export const ReimbursementModal: React.FC<ReimbursementModalProps> = ({
  isOpen,
  onClose,
  preselectedPaymentIds = []
}) => {
  const { createReimbursement } = useReimbursement();
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (data: CreateReimbursementData) => {
    setIsLoading(true);
    try {
      await createReimbursement(data);
      onClose();
    } catch (error) {
      // Error is handled in the form
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="w-full">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Create Reimbursement Request
                  </h3>
                  <button
                    type="button"
                    onClick={onClose}
                    className="bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="mt-3">
                  <ReimbursementForm
                    onSubmit={handleSubmit}
                    onCancel={onClose}
                    isLoading={isLoading}
                    preselectedPaymentIds={preselectedPaymentIds}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};