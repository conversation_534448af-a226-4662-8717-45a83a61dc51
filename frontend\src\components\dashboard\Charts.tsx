import React from 'react';

// Chart data interfaces
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
}

export interface TimeSeriesDataPoint {
  date: string;
  value: number;
  label?: string;
}

// Simple Line Chart Component
interface LineChartProps {
  data: TimeSeriesDataPoint[];
  width?: number;
  height?: number;
  color?: string;
  showDots?: boolean;
  showGrid?: boolean;
  className?: string;
}

export const LineChart: React.FC<LineChartProps> = ({
  data,
  width = 400,
  height = 200,
  color = '#3B82F6',
  showDots = true,
  showGrid = true,
  className = ''
}) => {
  if (data.length === 0) return null;

  const padding = 40;
  const chartWidth = width - padding * 2;
  const chartHeight = height - padding * 2;

  const maxValue = Math.max(...data.map(d => d.value));
  const minValue = Math.min(...data.map(d => d.value));
  const valueRange = maxValue - minValue || 1;

  const points = data.map((point, index) => {
    const x = padding + (index / (data.length - 1)) * chartWidth;
    const y = padding + chartHeight - ((point.value - minValue) / valueRange) * chartHeight;
    return { x, y, ...point };
  });

  const pathData = points.map((point, index) => 
    `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
  ).join(' ');

  return (
    <div className={className}>
      <svg width={width} height={height} className="overflow-visible">
        {/* Grid lines */}
        {showGrid && (
          <g className="opacity-20">
            {/* Horizontal grid lines */}
            {[0, 0.25, 0.5, 0.75, 1].map(ratio => (
              <line
                key={`h-${ratio}`}
                x1={padding}
                y1={padding + chartHeight * ratio}
                x2={padding + chartWidth}
                y2={padding + chartHeight * ratio}
                stroke="currentColor"
                strokeWidth={1}
              />
            ))}
            {/* Vertical grid lines */}
            {points.map((point, index) => (
              index % Math.ceil(points.length / 4) === 0 && (
                <line
                  key={`v-${index}`}
                  x1={point.x}
                  y1={padding}
                  x2={point.x}
                  y2={padding + chartHeight}
                  stroke="currentColor"
                  strokeWidth={1}
                />
              )
            ))}
          </g>
        )}

        {/* Chart line */}
        <path
          d={pathData}
          fill="none"
          stroke={color}
          strokeWidth={2}
          className="drop-shadow-sm"
        />

        {/* Data points */}
        {showDots && points.map((point, index) => (
          <circle
            key={index}
            cx={point.x}
            cy={point.y}
            r={4}
            fill={color}
            className="drop-shadow-sm"
          />
        ))}

        {/* Axis labels */}
        <g className="text-xs fill-gray-500">
          {/* Y-axis labels */}
          {[0, 0.5, 1].map(ratio => {
            const value = minValue + (maxValue - minValue) * (1 - ratio);
            return (
              <text
                key={`y-${ratio}`}
                x={padding - 10}
                y={padding + chartHeight * ratio + 4}
                textAnchor="end"
              >
                {value.toFixed(0)}
              </text>
            );
          })}
          
          {/* X-axis labels */}
          {points.map((point, index) => (
            index % Math.ceil(points.length / 3) === 0 && (
              <text
                key={`x-${index}`}
                x={point.x}
                y={height - 10}
                textAnchor="middle"
              >
                {new Date(point.date).toLocaleDateString('en-US', { month: 'short' })}
              </text>
            )
          ))}
        </g>
      </svg>
    </div>
  );
};

// Simple Bar Chart Component
interface BarChartProps {
  data: ChartDataPoint[];
  width?: number;
  height?: number;
  color?: string;
  className?: string;
  showValues?: boolean;
}

export const BarChart: React.FC<BarChartProps> = ({
  data,
  width = 400,
  height = 200,
  color = '#10B981',
  className = '',
  showValues = true
}) => {
  if (data.length === 0) return null;

  const padding = 40;
  const chartWidth = width - padding * 2;
  const chartHeight = height - padding * 2;

  const maxValue = Math.max(...data.map(d => d.value));
  const barWidth = chartWidth / data.length * 0.8;
  const barSpacing = chartWidth / data.length * 0.2;

  return (
    <div className={className}>
      <svg width={width} height={height} className="overflow-visible">
        {/* Bars */}
        {data.map((item, index) => {
          const barHeight = (item.value / maxValue) * chartHeight;
          const x = padding + index * (barWidth + barSpacing);
          const y = padding + chartHeight - barHeight;
          
          return (
            <g key={index}>
              <rect
                x={x}
                y={y}
                width={barWidth}
                height={barHeight}
                fill={item.color || color}
                className="drop-shadow-sm"
                rx={2}
              />
              
              {/* Value labels */}
              {showValues && (
                <text
                  x={x + barWidth / 2}
                  y={y - 5}
                  textAnchor="middle"
                  className="text-xs fill-gray-700 font-medium"
                >
                  {item.value}
                </text>
              )}
              
              {/* Category labels */}
              <text
                x={x + barWidth / 2}
                y={height - 10}
                textAnchor="middle"
                className="text-xs fill-gray-500"
              >
                {item.label}
              </text>
            </g>
          );
        })}
        
        {/* Y-axis line */}
        <line
          x1={padding}
          y1={padding}
          x2={padding}
          y2={padding + chartHeight}
          stroke="currentColor"
          strokeWidth={1}
          className="opacity-20"
        />
        
        {/* X-axis line */}
        <line
          x1={padding}
          y1={padding + chartHeight}
          x2={padding + chartWidth}
          y2={padding + chartHeight}
          stroke="currentColor"
          strokeWidth={1}
          className="opacity-20"
        />
      </svg>
    </div>
  );
};

// Donut Chart Component
interface DonutChartProps {
  data: ChartDataPoint[];
  size?: number;
  innerRadius?: number;
  className?: string;
  showLabels?: boolean;
}

export const DonutChart: React.FC<DonutChartProps> = ({
  data,
  size = 200,
  innerRadius = 60,
  className = '',
  showLabels = true
}) => {
  if (data.length === 0) return null;

  const radius = size / 2 - 10;
  const center = size / 2;
  const total = data.reduce((sum, item) => sum + item.value, 0);

  const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

  let currentAngle = 0;
  const segments = data.map((item, index) => {
    const percentage = item.value / total;
    const angle = percentage * 2 * Math.PI;
    const startAngle = currentAngle;
    const endAngle = currentAngle + angle;
    
    currentAngle += angle;

    const largeArcFlag = angle > Math.PI ? 1 : 0;
    const x1 = center + radius * Math.cos(startAngle);
    const y1 = center + radius * Math.sin(startAngle);
    const x2 = center + radius * Math.cos(endAngle);
    const y2 = center + radius * Math.sin(endAngle);
    
    const innerX1 = center + innerRadius * Math.cos(startAngle);
    const innerY1 = center + innerRadius * Math.sin(startAngle);
    const innerX2 = center + innerRadius * Math.cos(endAngle);
    const innerY2 = center + innerRadius * Math.sin(endAngle);

    const pathData = [
      `M ${x1} ${y1}`,
      `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
      `L ${innerX2} ${innerY2}`,
      `A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${innerX1} ${innerY1}`,
      'Z'
    ].join(' ');

    return {
      ...item,
      pathData,
      color: item.color || colors[index % colors.length],
      percentage: (percentage * 100).toFixed(1),
      labelX: center + (radius + innerRadius) / 2 * Math.cos(startAngle + angle / 2),
      labelY: center + (radius + innerRadius) / 2 * Math.sin(startAngle + angle / 2)
    };
  });

  return (
    <div className={className}>
      <svg width={size} height={size} className="overflow-visible">
        {/* Chart segments */}
        {segments.map((segment, index) => (
          <path
            key={index}
            d={segment.pathData}
            fill={segment.color}
            className="drop-shadow-sm hover:opacity-80 transition-opacity"
          />
        ))}
        
        {/* Center text */}
        <text
          x={center}
          y={center - 5}
          textAnchor="middle"
          className="text-lg font-semibold fill-gray-900"
        >
          {total}
        </text>
        <text
          x={center}
          y={center + 15}
          textAnchor="middle"
          className="text-xs fill-gray-500"
        >
          Total
        </text>
      </svg>
      
      {/* Legend */}
      {showLabels && (
        <div className="mt-4 space-y-2">
          {segments.map((segment, index) => (
            <div key={index} className="flex items-center space-x-2 text-sm">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: segment.color }}
              />
              <span className="text-gray-700">{segment.label}</span>
              <span className="text-gray-500">({segment.percentage}%)</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Area Chart Component
interface AreaChartProps {
  data: TimeSeriesDataPoint[];
  width?: number;
  height?: number;
  color?: string;
  fillOpacity?: number;
  className?: string;
}

export const AreaChart: React.FC<AreaChartProps> = ({
  data,
  width = 400,
  height = 200,
  color = '#8B5CF6',
  fillOpacity = 0.3,
  className = ''
}) => {
  if (data.length === 0) return null;

  const padding = 40;
  const chartWidth = width - padding * 2;
  const chartHeight = height - padding * 2;

  const maxValue = Math.max(...data.map(d => d.value));
  const minValue = Math.min(...data.map(d => d.value));
  const valueRange = maxValue - minValue || 1;

  const points = data.map((point, index) => {
    const x = padding + (index / (data.length - 1)) * chartWidth;
    const y = padding + chartHeight - ((point.value - minValue) / valueRange) * chartHeight;
    return { x, y, ...point };
  });

  const lineData = points.map((point, index) => 
    `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
  ).join(' ');

  const areaData = [
    lineData,
    `L ${points[points.length - 1].x} ${padding + chartHeight}`,
    `L ${points[0].x} ${padding + chartHeight}`,
    'Z'
  ].join(' ');

  return (
    <div className={className}>
      <svg width={width} height={height} className="overflow-visible">
        {/* Gradient definition */}
        <defs>
          <linearGradient id={`gradient-${color}`} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor={color} stopOpacity={fillOpacity} />
            <stop offset="100%" stopColor={color} stopOpacity={0} />
          </linearGradient>
        </defs>

        {/* Area fill */}
        <path
          d={areaData}
          fill={`url(#gradient-${color})`}
          className="drop-shadow-sm"
        />

        {/* Line */}
        <path
          d={lineData}
          fill="none"
          stroke={color}
          strokeWidth={2}
          className="drop-shadow-sm"
        />

        {/* Data points */}
        {points.map((point, index) => (
          <circle
            key={index}
            cx={point.x}
            cy={point.y}
            r={3}
            fill={color}
            className="drop-shadow-sm"
          />
        ))}
      </svg>
    </div>
  );
};