{"name": "subscription-tracker-frontend", "version": "0.1.0", "private": true, "dependencies": {"@hookform/resolvers": "^3.3.2", "@supabase/supabase-js": "^2.38.0", "@tanstack/react-query": "^5.8.4", "@tanstack/react-table": "^8.10.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.68", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "tailwindcss": "^3.3.6", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}}