import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { BusinessProvider } from './contexts/BusinessContext';
import { SubscriptionProvider } from './contexts/SubscriptionContext';
import { ExchangeRateProvider } from './contexts/ExchangeRateContext';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import { LoginPage } from './pages/LoginPage';
import { DashboardPage } from './pages/DashboardPage';
import { BusinessPage } from './pages/BusinessPage';
import { BusinessDetailPage } from './pages/BusinessDetailPage';
import { SubscriptionPage } from './pages/SubscriptionPage';
import { ExchangeRatePage } from './pages/ExchangeRatePage';
import { ProfilePage } from './pages/ProfilePage';
import { SubscriptionDetailPage } from './pages/SubscriptionDetailPage';
import './App.css';

function App() {
  return (
    <AuthProvider>
      <BusinessProvider>
        <SubscriptionProvider>
          <ExchangeRateProvider>
          <Router>
            <div className="App">
              <Routes>
                {/* Public routes */}
                <Route 
                  path="/login" 
                  element={
                    <ProtectedRoute requireAuth={false}>
                      <LoginPage />
                    </ProtectedRoute>
                  } 
                />
                
                {/* Protected routes */}
                <Route 
                  path="/dashboard" 
                  element={
                    <ProtectedRoute>
                      <DashboardPage />
                    </ProtectedRoute>
                  } 
                />
                
                <Route 
                  path="/businesses" 
                  element={
                    <ProtectedRoute>
                      <BusinessPage />
                    </ProtectedRoute>
                  } 
                />
                
                <Route 
                  path="/businesses/:id" 
                  element={
                    <ProtectedRoute>
                      <BusinessDetailPage />
                    </ProtectedRoute>
                  } 
                />
                
                <Route 
                  path="/subscriptions" 
                  element={
                    <ProtectedRoute>
                      <SubscriptionPage />
                    </ProtectedRoute>
                  } 
                />

                <Route 
                  path="/exchange-rates" 
                  element={
                    <ProtectedRoute>
                      <ExchangeRatePage />
                    </ProtectedRoute>
                  } 
                />

                <Route 
                  path="/profile" 
                  element={
                    <ProtectedRoute>
                      <ProfilePage />
                    </ProtectedRoute>
                  } 
                />

                <Route 
                  path="/subscriptions/:id" 
                  element={
                    <ProtectedRoute>
                      <SubscriptionDetailPage />
                    </ProtectedRoute>
                  } 
                />
                
                {/* Default redirect */}
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
                
                {/* Catch all route */}
                <Route path="*" element={<Navigate to="/dashboard" replace />} />
              </Routes>
            </div>
          </Router>
        </ExchangeRateProvider>
        </SubscriptionProvider>
      </BusinessProvider>
    </AuthProvider>
  );
}

export default App;